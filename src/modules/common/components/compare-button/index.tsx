'use client'

import { Scale, X } from 'lucide-react'
import { HttpTypes } from "@medusajs/types"
import { useCompare } from '@lib/context/compare-context'

interface CompareButtonProps {
  product: HttpTypes.StoreProduct
  variant?: 'default' | 'icon-only' | 'large'
  className?: string
}

const CompareButton = ({ product, variant = 'default', className = '' }: CompareButtonProps) => {
  const { addToCompare, removeFromCompare, isInCompare, canAddMore } = useCompare()
  const inCompare = isInCompare(product.id)

  const handleToggle = () => {
    if (inCompare) {
      removeFromCompare(product.id)
    } else {
      const success = addToCompare(product)
      if (!success) {
        // Could show a toast notification here
        alert('Maximum 4 products can be compared at once. Please remove a product first.')
      }
    }
  }

  const isDisabled = !inCompare && !canAddMore()

  const baseClasses = "transition-all duration-300 ease-brand active:scale-95"
  
  const variantClasses = {
    'default': `flex items-center gap-2 px-4 py-2 rounded-lg font-medium ${
      inCompare 
        ? 'bg-blue-50 text-blue-600 hover:bg-blue-100' 
        : isDisabled
        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
        : 'bg-secondary-100 text-text-primary hover:bg-blue-50 hover:text-blue-600'
    }`,
    'icon-only': `p-2 rounded-lg ${
      inCompare 
        ? 'bg-blue-50 text-blue-600 hover:bg-blue-100' 
        : isDisabled
        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
        : 'bg-secondary-100 text-text-secondary hover:bg-blue-50 hover:text-blue-600'
    }`,
    'large': `flex items-center gap-3 px-6 py-3 rounded-lg font-semibold text-lg ${
      inCompare 
        ? 'bg-blue-50 text-blue-600 hover:bg-blue-100' 
        : isDisabled
        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
        : 'bg-secondary-100 text-text-primary hover:bg-blue-50 hover:text-blue-600'
    }`
  }

  return (
    <button
      onClick={handleToggle}
      disabled={isDisabled}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      title={
        inCompare 
          ? 'Remove from comparison' 
          : isDisabled
          ? 'Maximum 4 products can be compared'
          : 'Add to comparison'
      }
    >
      {inCompare ? (
        <X className={`${variant === 'large' ? 'w-6 h-6' : 'w-5 h-5'}`} />
      ) : (
        <Scale className={`${variant === 'large' ? 'w-6 h-6' : 'w-5 h-5'}`} />
      )}
      {variant !== 'icon-only' && (
        <span>
          {inCompare ? 'Remove from Compare' : 'Add to Compare'}
        </span>
      )}
    </button>
  )
}

export default CompareButton
