'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { RefreshCw, Home, AlertTriangle, Mail } from 'lucide-react'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error)
  }, [error])

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Error Icon */}
        <div className="mb-8">
          <div className="w-32 h-32 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center mx-auto animate-pulse">
            <AlertTriangle className="w-16 h-16 text-white" />
          </div>
        </div>

        {/* Error Message */}
        <div className="space-y-6 mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-text-primary">
            Something went wrong!
          </h1>
          <p className="text-lg text-text-secondary leading-relaxed">
            We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.
          </p>
          
          {/* Error Details (Development only) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-left">
              <h3 className="font-semibold text-red-800 mb-2">Error Details:</h3>
              <pre className="text-sm text-red-700 overflow-auto">
                {error.message}
              </pre>
              {error.digest && (
                <p className="text-xs text-red-600 mt-2">
                  Error ID: {error.digest}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <button
            onClick={reset}
            className="inline-flex items-center gap-3 btn-primary hover:scale-105 transition-transform duration-300"
          >
            <RefreshCw className="w-5 h-5" />
            Try Again
          </button>
          
          <Link
            href="/"
            className="inline-flex items-center gap-3 btn-secondary hover:scale-105 transition-transform duration-300"
          >
            <Home className="w-5 h-5" />
            Back to Home
          </Link>
        </div>

        {/* Help Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
          <h3 className="text-xl font-semibold text-text-primary mb-6">
            Need Help?
          </h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Link
              href="/help"
              className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-300 group"
            >
              <div className="w-10 h-10 bg-blue-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                ❓
              </div>
              <div className="text-left">
                <div className="font-medium text-text-primary">Help Center</div>
                <div className="text-sm text-text-secondary">Find answers to common questions</div>
              </div>
            </Link>

            <Link
              href="/contact"
              className="flex items-center gap-3 p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-300 group"
            >
              <div className="w-10 h-10 bg-green-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Mail className="w-5 h-5 text-green-600" />
              </div>
              <div className="text-left">
                <div className="font-medium text-text-primary">Contact Support</div>
                <div className="text-sm text-text-secondary">Get help from our team</div>
              </div>
            </Link>
          </div>
        </div>

        {/* Status Message */}
        <div className="mt-8 p-6 bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl">
          <h4 className="font-semibold text-text-primary mb-2">🔧 We're on it!</h4>
          <p className="text-text-secondary text-sm">
            Our technical team has been automatically notified of this issue. 
            We're working hard to get everything back to normal as quickly as possible.
          </p>
        </div>
      </div>
    </div>
  )
}
