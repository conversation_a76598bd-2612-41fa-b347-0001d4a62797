'use client'

/**
 * Product Comparison Context Provider
 *
 * Manages product comparison functionality including:
 * - Adding/removing products from comparison (max 4 products)
 * - Persisting comparison data to localStorage
 * - Providing comparison count and status checks
 *
 * <AUTHOR> Store Team
 * @version 1.0.0
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react'
import { HttpTypes } from "@medusajs/types"

interface CompareItem {
  id: string
  title: string
  handle: string
  thumbnail?: string
  price?: number
  metadata?: any
  variants?: any[]
  categories?: any[]
  addedAt: string
}

interface CompareState {
  items: CompareItem[]
  isLoading: boolean
  maxItems: number
}

type CompareAction =
  | { type: 'ADD_ITEM'; payload: CompareItem }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_COMPARE' }
  | { type: 'LOAD_COMPARE'; payload: CompareItem[] }
  | { type: 'SET_LOADING'; payload: boolean }

interface CompareContextType {
  state: CompareState
  addToCompare: (product: HttpTypes.StoreProduct) => boolean
  removeFromCompare: (productId: string) => void
  clearCompare: () => void
  isInCompare: (productId: string) => boolean
  getCompareCount: () => number
  canAddMore: () => boolean
}

const CompareContext = createContext<CompareContextType | undefined>(undefined)

const compareReducer = (state: CompareState, action: CompareAction): CompareState => {
  switch (action.type) {
    case 'ADD_ITEM':
      if (state.items.some(item => item.id === action.payload.id)) {
        return state // Item already exists
      }
      if (state.items.length >= state.maxItems) {
        return state // Max items reached
      }
      return {
        ...state,
        items: [...state.items, action.payload]
      }
    
    case 'REMOVE_ITEM':
      return {
        ...state,
        items: state.items.filter(item => item.id !== action.payload)
      }
    
    case 'CLEAR_COMPARE':
      return {
        ...state,
        items: []
      }
    
    case 'LOAD_COMPARE':
      return {
        ...state,
        items: action.payload.slice(0, state.maxItems), // Ensure max limit
        isLoading: false
      }
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      }
    
    default:
      return state
  }
}

export const CompareProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(compareReducer, {
    items: [],
    isLoading: true,
    maxItems: 4 // Maximum 4 products for comparison
  })

  // Load compare list from localStorage on mount
  useEffect(() => {
    const loadCompare = () => {
      try {
        const saved = localStorage.getItem('compareList')
        if (saved) {
          const items = JSON.parse(saved)
          dispatch({ type: 'LOAD_COMPARE', payload: items })
        } else {
          dispatch({ type: 'SET_LOADING', payload: false })
        }
      } catch (error) {
        console.error('Error loading compare list:', error)
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    }

    loadCompare()
  }, [])

  // Save to localStorage whenever items change
  useEffect(() => {
    if (!state.isLoading) {
      localStorage.setItem('compareList', JSON.stringify(state.items))
    }
  }, [state.items, state.isLoading])

  const addToCompare = useCallback((product: HttpTypes.StoreProduct): boolean => {
    if (state.items.length >= state.maxItems) {
      return false // Cannot add more items
    }

    const compareItem: CompareItem = {
      id: product.id,
      title: product.title,
      handle: product.handle,
      thumbnail: product.thumbnail,
      price: product.variants?.[0]?.calculated_price?.calculated_amount,
      metadata: product.metadata,
      variants: product.variants,
      categories: product.categories,
      addedAt: new Date().toISOString()
    }

    dispatch({ type: 'ADD_ITEM', payload: compareItem })
    console.log(`Added ${product.title} to compare`)
    return true
  }, [state.items.length, state.maxItems])

  const removeFromCompare = useCallback((productId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: productId })
    console.log('Removed from compare')
  }, [])

  const clearCompare = useCallback(() => {
    dispatch({ type: 'CLEAR_COMPARE' })
    console.log('Compare list cleared')
  }, [])

  const isInCompare = useCallback((productId: string): boolean => {
    return state.items.some(item => item.id === productId)
  }, [state.items])

  const getCompareCount = useCallback((): number => {
    return state.items.length
  }, [state.items])

  const canAddMore = useCallback((): boolean => {
    return state.items.length < state.maxItems
  }, [state.items.length, state.maxItems])

  const value: CompareContextType = {
    state,
    addToCompare,
    removeFromCompare,
    clearCompare,
    isInCompare,
    getCompareCount,
    canAddMore
  }

  return (
    <CompareContext.Provider value={value}>
      {children}
    </CompareContext.Provider>
  )
}

export const useCompare = (): CompareContextType => {
  const context = useContext(CompareContext)
  if (context === undefined) {
    throw new Error('useCompare must be used within a CompareProvider')
  }
  return context
}

export default CompareContext
