'use client'

import Link from 'next/link'
import { CheckCircle, Palette, Type, MousePointer, Zap, Layout, ArrowRight } from 'lucide-react'

export default function BrandShowcasePage() {
  const completedFeatures = [
    {
      icon: Palette,
      title: "Brand Colors",
      description: "Primary (#5A5AFF), Secondary (#F2F2F2), Accent (#FFC107)",
      status: "完成"
    },
    {
      icon: Type,
      title: "Typography System",
      description: "Inter & Source Serif Pro fonts with responsive scaling",
      status: "完成"
    },
    {
      icon: MousePointer,
      title: "8px Grid System",
      description: "Consistent spacing based on 8px increments",
      status: "完成"
    },
    {
      icon: Zap,
      title: "Animation System",
      description: "Cubic-bezier(0.4, 0, 0.2, 1) with 300ms default timing",
      status: "完成"
    },
    {
      icon: Layout,
      title: "Component Library",
      description: "Buttons, cards, and interactive elements",
      status: "完成"
    }
  ]

  const pages = [
    {
      title: "首页 (Homepage)",
      description: "完整的品牌首页，包含 Hero、特色产品、品牌故事和用户评价",
      url: "/us",
      features: ["视差效果 Hero", "水平滚动产品卡片", "左右分栏品牌故事", "轮播用户评价"]
    },
    {
      title: "设计系统展示",
      description: "完整的设计语言和组件库展示",
      url: "/us/design-system",
      features: ["颜色系统", "字体系统", "按钮组件", "动画效果"]
    },
    {
      title: "PetShop 风格产品页",
      description: "高端产品展示页面，默认应用于所有产品",
      url: "/us/products/petshop-smart-pet-feeder-hd-camera",
      features: ["标签页布局", "折扣标签", "信任标识", "详细规格"]
    },
    {
      title: "Zustand 状态管理演示",
      description: "展示全局状态管理和购物车功能",
      url: "/us/zustand-demo",
      features: ["购物车状态", "主题切换", "愿望清单", "数据持久化"]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50">
      <div className="content-container py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <CheckCircle className="w-4 h-4" />
            项目完成
          </div>
          <h1 className="hero-title mb-6">
            🎨 品牌设计系统
            <span className="text-gradient block">完整实现</span>
          </h1>
          <p className="hero-subtitle max-w-3xl mx-auto">
            从零开始构建的完整设计系统，包含品牌视觉规范、组件库、动画效果和响应式布局。
            所有页面都采用统一的设计语言，提供一致的用户体验。
          </p>
        </div>

        {/* Completed Features */}
        <div className="mb-16">
          <h2 className="section-title text-center mb-8">✅ 已完成功能</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {completedFeatures.map((feature, index) => (
              <div 
                key={index}
                className="bg-white rounded-2xl p-6 shadow-lg card-hover"
              >
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <feature.icon className="w-6 h-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-text-primary">{feature.title}</h3>
                      <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                        {feature.status}
                      </span>
                    </div>
                    <p className="text-text-secondary text-sm">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Pages Showcase */}
        <div className="mb-16">
          <h2 className="section-title text-center mb-8">🚀 页面展示</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {pages.map((page, index) => (
              <div 
                key={index}
                className="bg-white rounded-2xl p-8 shadow-lg card-hover group"
              >
                <h3 className="text-xl font-bold text-text-primary mb-3 group-hover:text-primary transition-colors duration-300">
                  {page.title}
                </h3>
                <p className="text-text-secondary mb-6">
                  {page.description}
                </p>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-text-primary mb-3">主要特性：</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {page.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        <span className="text-sm text-text-secondary">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Link
                  href={page.url}
                  className="inline-flex items-center gap-2 btn-primary group-hover:scale-105 transition-transform duration-300"
                >
                  查看页面
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            ))}
          </div>
        </div>

        {/* Technical Implementation */}
        <div className="bg-white rounded-2xl p-8 shadow-lg mb-16">
          <h2 className="section-title mb-8">🛠️ 技术实现</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-4">设计系统配置</h3>
              <ul className="space-y-2 text-text-secondary">
                <li>• <strong>Tailwind CSS</strong> - 自定义配置文件</li>
                <li>• <strong>Google Fonts</strong> - Inter & Source Serif Pro</li>
                <li>• <strong>Lucide React</strong> - 图标库集成</li>
                <li>• <strong>CSS 变量</strong> - 动态主题支持</li>
                <li>• <strong>响应式设计</strong> - 移动端优先</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-4">动画与交互</h3>
              <ul className="space-y-2 text-text-secondary">
                <li>• <strong>CSS Keyframes</strong> - 自定义动画</li>
                <li>• <strong>Intersection Observer</strong> - 滚动触发</li>
                <li>• <strong>Hover Effects</strong> - 悬停状态</li>
                <li>• <strong>Transition Timing</strong> - 统一缓动函数</li>
                <li>• <strong>Transform Effects</strong> - 3D 变换</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="text-center">
          <h2 className="section-title mb-6">🎯 下一步计划</h2>
          <div className="bg-gradient-to-r from-primary-100 to-accent-100 rounded-2xl p-8 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-2xl mb-2">📱</div>
                <h3 className="font-semibold text-text-primary mb-2">移动端优化</h3>
                <p className="text-text-secondary text-sm">进一步优化移动端体验</p>
              </div>
              <div>
                <div className="text-2xl mb-2">🔍</div>
                <h3 className="font-semibold text-text-primary mb-2">SEO 优化</h3>
                <p className="text-text-secondary text-sm">结构化数据和性能优化</p>
              </div>
              <div>
                <div className="text-2xl mb-2">🧪</div>
                <h3 className="font-semibold text-text-primary mb-2">A/B 测试</h3>
                <p className="text-text-secondary text-sm">用户体验持续优化</p>
              </div>
            </div>
          </div>
          
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/us" className="btn-primary">
              查看首页
            </Link>
            <Link href="/us/design-system" className="btn-secondary">
              设计系统
            </Link>
            <Link href="/us/store" className="btn-accent">
              产品商店
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
