#!/bin/bash

# 云服务器部署脚本
echo "🚀 开始部署到云服务器..."

# 1. 安装依赖
sudo apt update
sudo apt install -y docker.io docker-compose nginx nodejs npm

# 2. 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 3. 创建项目目录
mkdir -p /var/www/pet-ecommerce
cd /var/www/pet-ecommerce

# 4. 上传项目文件 (需要手动执行)
echo "📁 请将项目文件上传到: /var/www/pet-ecommerce/"

# 5. 启动数据库和Redis
docker-compose -f docker-compose.simple.yml up -d

# 6. 恢复数据库
echo "💾 恢复数据库数据..."
# docker exec -i medusa_postgres psql -U medusa_user -d medusa_db < backup.sql

# 7. 安装依赖并启动服务
cd medusa-backend
npm install
npm run build

cd ..
npm install
npm run build

# 8. 配置Nginx
echo "⚙️ 配置Nginx反向代理..."

echo "✅ 部署完成！"
echo "🌐 前端: http://your-domain.com"
echo "👨‍💼 管理后台: http://your-domain.com/admin"
