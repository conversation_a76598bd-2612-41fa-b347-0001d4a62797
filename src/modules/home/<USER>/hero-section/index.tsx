'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ChevronRight, Play, Heart, Star } from 'lucide-react'

const HeroSection = () => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(0)

  const slides = [
    {
      id: 1,
      title: "Smart Pet Care",
      subtitle: "Technology Meets Love",
      description: "Discover our revolutionary smart pet feeders and monitoring systems designed to keep your furry friends happy and healthy, even when you're away.",
      image: "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
      cta: "Shop Smart Feeders",
      ctaLink: "/us/store?category=feeders"
    },
    {
      id: 2,
      title: "Premium Pet Nutrition",
      subtitle: "Health First",
      description: "From automatic feeders to water fountains, ensure your pets get the nutrition and hydration they need with our premium product line.",
      image: "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
      cta: "Explore Nutrition",
      ctaLink: "/us/store?category=nutrition"
    },
    {
      id: 3,
      title: "Interactive Play",
      subtitle: "Fun & Engagement",
      description: "Keep your pets entertained and active with our collection of interactive toys and smart play systems that adapt to your pet's behavior.",
      image: "https://images.unsplash.com/photo-**********-03cce0bbc87b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
      cta: "Shop Toys",
      ctaLink: "/us/store?category=toys"
    }
  ]

  useEffect(() => {
    setIsLoaded(true)
    
    // Auto-slide functionality
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [slides.length])

  const currentSlideData = slides[currentSlide]

  return (
    <section className="relative h-screen overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50">
      {/* Background Image with Parallax */}
      <div className="absolute inset-0 parallax-container">
        <div 
          className={`absolute inset-0 bg-cover bg-center bg-no-repeat parallax-element transition-all duration-1200 ease-brand ${
            isLoaded ? 'animate-hero-blur-to-clear' : 'filter blur-sm scale-105'
          }`}
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.3)), url(${currentSlideData.image})`,
            transform: `translateY(${currentSlide * -2}px)`,
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="content-container">
          <div className="max-w-4xl">
            {/* Subtitle */}
            <div 
              className={`inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6 transition-all duration-600 ease-brand ${
                isLoaded ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
              }`}
              style={{ animationDelay: '200ms' }}
            >
              <Heart className="w-4 h-4 text-accent" />
              <span className="text-white font-medium text-sm">
                {currentSlideData.subtitle}
              </span>
            </div>

            {/* Main Title */}
            <h1 
              className={`hero-title text-white mb-6 transition-all duration-600 ease-brand ${
                isLoaded ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
              }`}
              style={{ animationDelay: '400ms' }}
            >
              {currentSlideData.title}
            </h1>

            {/* Description */}
            <p 
              className={`hero-subtitle text-white/90 mb-8 max-w-2xl transition-all duration-600 ease-brand ${
                isLoaded ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
              }`}
              style={{ animationDelay: '600ms' }}
            >
              {currentSlideData.description}
            </p>

            {/* CTA Buttons */}
            <div 
              className={`flex flex-col sm:flex-row gap-4 transition-all duration-600 ease-brand ${
                isLoaded ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
              }`}
              style={{ animationDelay: '800ms' }}
            >
              <Link
                href={currentSlideData.ctaLink}
                className="group inline-flex items-center gap-2 bg-primary text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 ease-brand hover:bg-primary-600 hover:shadow-xl hover:shadow-primary/30 hover:scale-105 active:scale-95"
              >
                {currentSlideData.cta}
                <ChevronRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
              </Link>

              <button className="group inline-flex items-center gap-3 bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 ease-brand hover:bg-white/30 hover:scale-105 active:scale-95">
                <Play className="w-5 h-5" />
                Watch Demo
              </button>
            </div>

            {/* Trust Indicators */}
            <div 
              className={`flex items-center gap-6 mt-12 transition-all duration-600 ease-brand ${
                isLoaded ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
              }`}
              style={{ animationDelay: '1000ms' }}
            >
              <div className="flex items-center gap-2 text-white/80">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-accent text-accent" />
                  ))}
                </div>
                <span className="text-sm font-medium">4.9/5 Rating</span>
              </div>
              
              <div className="h-4 w-px bg-white/30" />
              
              <div className="text-white/80 text-sm font-medium">
                10,000+ Happy Pets
              </div>
              
              <div className="h-4 w-px bg-white/30" />
              
              <div className="text-white/80 text-sm font-medium">
                Free Shipping
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex gap-3">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ease-brand ${
                index === currentSlide 
                  ? 'bg-white scale-125' 
                  : 'bg-white/50 hover:bg-white/75'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 right-8 z-20">
        <div className="flex flex-col items-center gap-2 text-white/70">
          <span className="text-xs font-medium rotate-90 origin-center">Scroll</span>
          <div className="w-px h-8 bg-white/30 animate-pulse" />
        </div>
      </div>
    </section>
  )
}

export default HeroSection
