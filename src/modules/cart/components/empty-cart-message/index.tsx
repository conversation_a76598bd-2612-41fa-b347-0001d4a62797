import { <PERSON><PERSON>ag, ArrowR<PERSON>, Heart, Star } from "lucide-react"
import LocalizedClientLink from "@modules/common/components/localized-client-link"

const EmptyCartMessage = () => {
  return (
    <div className="bg-white rounded-2xl shadow-lg p-12 text-center" data-testid="empty-cart-message">
      {/* Empty Cart Illustration */}
      <div className="mb-8">
        <div className="w-32 h-32 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-full flex items-center justify-center mx-auto mb-6">
          <ShoppingBag className="w-16 h-16 text-text-secondary" />
        </div>

        <h2 className="text-3xl font-bold text-text-primary mb-4">
          Your cart is empty
        </h2>

        <p className="text-lg text-text-secondary mb-8 max-w-md mx-auto">
          Looks like you haven't added any items to your cart yet.
          Discover our amazing pet products and treat your furry friends!
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
        <LocalizedClientLink
          href="/store"
          className="inline-flex items-center gap-3 btn-primary hover:scale-105 transition-transform duration-300"
        >
          Explore Products
          <ArrowRight className="w-5 h-5" />
        </LocalizedClientLink>

        <LocalizedClientLink
          href="/store?category=feeders"
          className="inline-flex items-center gap-3 btn-secondary hover:scale-105 transition-transform duration-300"
        >
          <Heart className="w-5 h-5" />
          Popular Items
        </LocalizedClientLink>
      </div>

      {/* Featured Categories */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
        <LocalizedClientLink
          href="/store?category=feeders"
          className="group p-6 bg-primary-50 rounded-xl hover:bg-primary-100 transition-colors duration-300"
        >
          <div className="text-4xl mb-3">🍽️</div>
          <h3 className="font-semibold text-text-primary mb-2 group-hover:text-primary transition-colors duration-300">
            Smart Feeders
          </h3>
          <p className="text-sm text-text-secondary">
            Automatic feeding solutions
          </p>
        </LocalizedClientLink>

        <LocalizedClientLink
          href="/store?category=toys"
          className="group p-6 bg-accent-50 rounded-xl hover:bg-accent-100 transition-colors duration-300"
        >
          <div className="text-4xl mb-3">🎾</div>
          <h3 className="font-semibold text-text-primary mb-2 group-hover:text-accent-700 transition-colors duration-300">
            Interactive Toys
          </h3>
          <p className="text-sm text-text-secondary">
            Fun and engaging playtime
          </p>
        </LocalizedClientLink>

        <LocalizedClientLink
          href="/store?category=accessories"
          className="group p-6 bg-green-50 rounded-xl hover:bg-green-100 transition-colors duration-300"
        >
          <div className="text-4xl mb-3">🎀</div>
          <h3 className="font-semibold text-text-primary mb-2 group-hover:text-green-700 transition-colors duration-300">
            Accessories
          </h3>
          <p className="text-sm text-text-secondary">
            Stylish pet accessories
          </p>
        </LocalizedClientLink>
      </div>

      {/* Trust Indicators */}
      <div className="mt-12 pt-8 border-t border-secondary-200">
        <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-text-secondary">
          <div className="flex items-center gap-2">
            <Star className="w-4 h-4 text-accent" />
            <span>4.9/5 Customer Rating</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Free Shipping Over $50</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>30-Day Returns</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EmptyCartMessage
