'use client'

/**
 * Wishlist Context Provider
 *
 * Manages user's wishlist state including:
 * - Adding/removing products from wishlist
 * - Persisting wishlist data to localStorage
 * - Providing wishlist count and status checks
 *
 * <AUTHOR> Store Team
 * @version 1.0.0
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react'
import { HttpTypes } from "@medusajs/types"

interface WishlistItem {
  id: string
  title: string
  handle: string
  thumbnail?: string
  price?: number
  addedAt: string
}

interface WishlistState {
  items: WishlistItem[]
  isLoading: boolean
}

type WishlistAction =
  | { type: 'ADD_ITEM'; payload: WishlistItem }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_WISHLIST' }
  | { type: 'LOAD_WISHLIST'; payload: WishlistItem[] }
  | { type: 'SET_LOADING'; payload: boolean }

interface WishlistContextType {
  state: WishlistState
  addToWishlist: (product: HttpTypes.StoreProduct) => void
  removeFromWishlist: (productId: string) => void
  clearWishlist: () => void
  isInWishlist: (productId: string) => boolean
  getWishlistCount: () => number
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined)

const wishlistReducer = (state: WishlistState, action: WishlistAction): WishlistState => {
  switch (action.type) {
    case 'ADD_ITEM':
      if (state.items.some(item => item.id === action.payload.id)) {
        return state // Item already exists
      }
      return {
        ...state,
        items: [...state.items, action.payload]
      }
    
    case 'REMOVE_ITEM':
      return {
        ...state,
        items: state.items.filter(item => item.id !== action.payload)
      }
    
    case 'CLEAR_WISHLIST':
      return {
        ...state,
        items: []
      }
    
    case 'LOAD_WISHLIST':
      return {
        ...state,
        items: action.payload,
        isLoading: false
      }
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      }
    
    default:
      return state
  }
}

export const WishlistProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(wishlistReducer, {
    items: [],
    isLoading: true
  })

  // Load wishlist from localStorage on mount
  useEffect(() => {
    const loadWishlist = () => {
      try {
        const saved = localStorage.getItem('wishlist')
        if (saved) {
          const items = JSON.parse(saved)
          dispatch({ type: 'LOAD_WISHLIST', payload: items })
        } else {
          dispatch({ type: 'SET_LOADING', payload: false })
        }
      } catch (error) {
        console.error('Error loading wishlist:', error)
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    }

    loadWishlist()
  }, [])

  // Save to localStorage whenever items change
  useEffect(() => {
    if (!state.isLoading) {
      localStorage.setItem('wishlist', JSON.stringify(state.items))
    }
  }, [state.items, state.isLoading])

  const addToWishlist = useCallback((product: HttpTypes.StoreProduct) => {
    const wishlistItem: WishlistItem = {
      id: product.id,
      title: product.title,
      handle: product.handle,
      thumbnail: product.thumbnail,
      price: product.variants?.[0]?.calculated_price?.calculated_amount,
      addedAt: new Date().toISOString()
    }

    dispatch({ type: 'ADD_ITEM', payload: wishlistItem })
  }, [])

  const removeFromWishlist = useCallback((productId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: productId })
  }, [])

  const clearWishlist = useCallback(() => {
    dispatch({ type: 'CLEAR_WISHLIST' })
  }, [])

  const isInWishlist = useCallback((productId: string): boolean => {
    return state.items.some(item => item.id === productId)
  }, [state.items])

  const getWishlistCount = useCallback((): number => {
    return state.items.length
  }, [state.items])

  const value: WishlistContextType = {
    state,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInWishlist,
    getWishlistCount
  }

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  )
}

export const useWishlist = (): WishlistContextType => {
  const context = useContext(WishlistContext)
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider')
  }
  return context
}

export default WishlistContext
