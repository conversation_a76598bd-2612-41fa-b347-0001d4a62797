'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Search, X, Clock, TrendingUp } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { HttpTypes } from "@medusajs/types"

interface SearchProps {
  isOpen: boolean
  onClose: () => void
  countryCode: string
}

interface SearchResult {
  id: string
  title: string
  handle: string
  thumbnail?: string
  price?: number
  category?: string
}

const SearchModal = ({ isOpen, onClose, countryCode }: SearchProps) => {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [popularSearches] = useState([
    'Smart Feeder', 'Pet Camera', 'Interactive Toy', 'Water Fountain', 'Pet Bed'
  ])
  
  const router = useRouter()
  const inputRef = useRef<HTMLInputElement>(null)
  const debounceRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [])

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])

  const saveRecentSearch = useCallback((searchQuery: string) => {
    const updated = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5)
    setRecentSearches(updated)
    localStorage.setItem('recentSearches', JSON.stringify(updated))
  }, [recentSearches])

  const searchProducts = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setLoading(true)
    try {
      const backendUrl = process.env.NEXT_PUBLIC_MEDUSA_BACKEND_URL || 'http://localhost:9000'
      const apiKey = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || 'pk_53915089e6efa57e1da508d6bc60db568d9fb1424190423024964d4e5d4a2846'

      const response = await fetch(`${backendUrl}/store/products?q=${encodeURIComponent(searchQuery)}&limit=8`, {
        headers: {
          'x-publishable-api-key': apiKey
        }
      })

      if (response.ok) {
        const data = await response.json()
        const searchResults: SearchResult[] = (data.products || []).map((product: HttpTypes.StoreProduct) => ({
          id: product.id,
          title: product.title,
          handle: product.handle,
          thumbnail: product.thumbnail,
          price: product.variants?.[0]?.calculated_price?.calculated_amount,
          category: product.categories?.[0]?.name
        }))
        setResults(searchResults)
      } else {
        console.error('Search failed:', response.status, response.statusText)
        setResults([])
      }
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
    } finally {
      setLoading(false)
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)

    // Debounce search
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      searchProducts(value)
    }, 300)
  }

  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      saveRecentSearch(searchQuery.trim())
      router.push(`/${countryCode}/search?q=${encodeURIComponent(searchQuery.trim())}`)
      onClose()
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(query)
    } else if (e.key === 'Escape') {
      onClose()
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price / 100)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
      <div className="container mx-auto px-4 pt-20">
        <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden">
          {/* Search Input */}
          <div className="flex items-center gap-4 p-6 border-b border-secondary-200">
            <Search className="w-6 h-6 text-text-secondary" />
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="Search for products..."
              className="flex-1 text-lg outline-none text-text-primary placeholder-text-secondary"
            />
            <button
              onClick={onClose}
              className="p-2 hover:bg-secondary-100 rounded-lg transition-colors duration-300"
            >
              <X className="w-5 h-5 text-text-secondary" />
            </button>
          </div>

          {/* Search Content */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="p-6 text-center">
                <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-text-secondary">Searching...</p>
              </div>
            ) : query && results.length > 0 ? (
              <div className="p-4">
                <h3 className="text-sm font-semibold text-text-secondary mb-4 px-2">
                  Search Results ({results.length})
                </h3>
                <div className="space-y-2">
                  {results.map((result) => (
                    <button
                      key={result.id}
                      onClick={() => {
                        router.push(`/${countryCode}/products/${result.handle}`)
                        onClose()
                      }}
                      className="w-full flex items-center gap-4 p-3 hover:bg-secondary-50 rounded-lg transition-colors duration-300 text-left"
                    >
                      <div className="w-12 h-12 bg-secondary-100 rounded-lg overflow-hidden flex-shrink-0">
                        {result.thumbnail ? (
                          <img
                            src={result.thumbnail}
                            alt={result.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-text-secondary">
                            📦
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-text-primary truncate">{result.title}</h4>
                        <div className="flex items-center gap-2 text-sm text-text-secondary">
                          {result.category && <span>{result.category}</span>}
                          {result.price && (
                            <>
                              <span>•</span>
                              <span className="font-medium text-primary">{formatPrice(result.price)}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
                
                {results.length >= 8 && (
                  <button
                    onClick={() => handleSearch(query)}
                    className="w-full mt-4 p-3 text-center text-primary hover:bg-primary-50 rounded-lg transition-colors duration-300 font-medium"
                  >
                    View all results for "{query}"
                  </button>
                )}
              </div>
            ) : query && !loading ? (
              <div className="p-6 text-center">
                <div className="text-4xl mb-4">🔍</div>
                <h3 className="font-semibold text-text-primary mb-2">No results found</h3>
                <p className="text-text-secondary">Try searching for something else</p>
              </div>
            ) : (
              <div className="p-4 space-y-6">
                {/* Recent Searches */}
                {recentSearches.length > 0 && (
                  <div>
                    <h3 className="text-sm font-semibold text-text-secondary mb-3 px-2 flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      Recent Searches
                    </h3>
                    <div className="space-y-1">
                      {recentSearches.map((search, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            setQuery(search)
                            searchProducts(search)
                          }}
                          className="w-full text-left p-3 hover:bg-secondary-50 rounded-lg transition-colors duration-300 text-text-primary"
                        >
                          {search}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Popular Searches */}
                <div>
                  <h3 className="text-sm font-semibold text-text-secondary mb-3 px-2 flex items-center gap-2">
                    <TrendingUp className="w-4 h-4" />
                    Popular Searches
                  </h3>
                  <div className="space-y-1">
                    {popularSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          setQuery(search)
                          searchProducts(search)
                        }}
                        className="w-full text-left p-3 hover:bg-secondary-50 rounded-lg transition-colors duration-300 text-text-primary"
                      >
                        {search}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SearchModal
