import { Metadata } from "next"
import WishlistTemplate from "@modules/wishlist/templates"

export const metadata: Metadata = {
  title: "Wishlist | Pet Store",
  description: "Your saved pet products and favorites. Keep track of items you love and add them to your cart when ready.",
}

type Props = {
  params: Promise<{ countryCode: string }>
}

export default async function WishlistPage({ params }: Props) {
  const { countryCode } = await params

  return <WishlistTemplate countryCode={countryCode} />
}
