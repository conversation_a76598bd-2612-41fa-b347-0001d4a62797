version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: medusa_postgres
    environment:
      POSTGRES_USER: medusa_user
      POSTGRES_PASSWORD: medusa_password
      POSTGRES_DB: medusa_db
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - medusa_network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: medusa_redis
    ports:
      - "6379:6379"
    networks:
      - medusa_network

volumes:
  postgres_data:

networks:
  medusa_network:
    driver: bridge
