'use client'

import Link from 'next/link'
import { CheckCircle, Search, Heart, Scale, Star, Gift, Smartphone, TrendingUp, ArrowRight, Zap } from 'lucide-react'

export default function FeaturesShowcasePage() {
  const newFeatures = [
    {
      icon: Search,
      title: "全局搜索功能",
      description: "智能搜索系统，支持实时搜索、自动完成和搜索历史",
      features: ["实时搜索结果", "搜索历史记录", "热门搜索推荐", "搜索结果页面"],
      demoUrl: "/us/search",
      status: "完成"
    },
    {
      icon: Heart,
      title: "愿望清单系统",
      description: "完整的愿望清单功能，让用户保存喜爱的产品",
      features: ["添加/删除产品", "愿望清单页面", "本地存储", "计数显示"],
      demoUrl: "/us/wishlist",
      status: "完成"
    },
    {
      icon: Scale,
      title: "产品比较功能",
      description: "并排比较多个产品的规格和特性",
      features: ["最多4个产品", "规格对比表", "浮动比较栏", "详细对比"],
      demoUrl: "/us/compare",
      status: "完成"
    }
  ]

  const upcomingFeatures = [
    {
      icon: Star,
      title: "评价和评论系统",
      description: "用户可以对产品进行评分和评论",
      features: ["5星评分", "文字评论", "图片上传", "评论筛选"],
      status: "规划中"
    },
    {
      icon: Gift,
      title: "优惠券和促销",
      description: "完整的优惠券和促销活动系统",
      features: ["折扣码", "满减活动", "限时促销", "会员专享"],
      status: "规划中"
    },
    {
      icon: Smartphone,
      title: "移动端优化",
      description: "进一步优化移动端体验和手势操作",
      features: ["手势导航", "触摸优化", "PWA支持", "离线功能"],
      status: "规划中"
    },
    {
      icon: TrendingUp,
      title: "SEO优化",
      description: "全面的搜索引擎优化和性能提升",
      features: ["结构化数据", "站点地图", "页面速度", "Core Web Vitals"],
      status: "规划中"
    }
  ]

  const technicalHighlights = [
    {
      title: "状态管理",
      description: "使用 React Context 和 useReducer 实现复杂状态管理",
      tech: "React Context + useReducer"
    },
    {
      title: "本地存储",
      description: "愿望清单和比较列表数据持久化存储",
      tech: "localStorage + JSON"
    },
    {
      title: "实时搜索",
      description: "防抖搜索和异步数据获取",
      tech: "Debounce + Async/Await"
    },
    {
      title: "响应式设计",
      description: "完美适配所有设备尺寸",
      tech: "Tailwind CSS + Mobile First"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50">
      <div className="content-container py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <CheckCircle className="w-4 h-4" />
            功能完善完成
          </div>
          <h1 className="hero-title mb-6">
            🚀 项目功能完善
            <span className="text-gradient block">全面升级</span>
          </h1>
          <p className="hero-subtitle max-w-3xl mx-auto">
            新增了搜索、愿望清单、产品比较等核心电商功能，大幅提升用户体验和商业价值。
            每个功能都经过精心设计，确保与现有系统完美集成。
          </p>
        </div>

        {/* New Features */}
        <div className="mb-16">
          <h2 className="section-title text-center mb-8">✨ 新增功能</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {newFeatures.map((feature, index) => (
              <div 
                key={index}
                className="bg-white rounded-2xl p-6 shadow-lg card-hover group"
              >
                <div className="flex items-start gap-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <feature.icon className="w-6 h-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-text-primary">{feature.title}</h3>
                      <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                        {feature.status}
                      </span>
                    </div>
                    <p className="text-text-secondary text-sm mb-4">{feature.description}</p>
                  </div>
                </div>
                
                <div className="space-y-3 mb-6">
                  <h4 className="font-medium text-text-primary text-sm">主要特性：</h4>
                  <div className="grid grid-cols-2 gap-1">
                    {feature.features.map((feat, featIndex) => (
                      <div key={featIndex} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        <span className="text-xs text-text-secondary">{feat}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Link
                  href={feature.demoUrl}
                  className="inline-flex items-center gap-2 w-full justify-center btn-primary group-hover:scale-105 transition-transform duration-300"
                >
                  体验功能
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            ))}
          </div>
        </div>

        {/* Technical Highlights */}
        <div className="bg-white rounded-2xl p-8 shadow-lg mb-16">
          <h2 className="section-title mb-8">🛠️ 技术亮点</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {technicalHighlights.map((highlight, index) => (
              <div key={index} className="flex items-start gap-4 p-4 bg-secondary-50 rounded-lg">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Zap className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-text-primary mb-2">{highlight.title}</h3>
                  <p className="text-text-secondary text-sm mb-2">{highlight.description}</p>
                  <span className="inline-block bg-primary-100 text-primary-700 px-2 py-1 rounded text-xs font-medium">
                    {highlight.tech}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Upcoming Features */}
        <div className="mb-16">
          <h2 className="section-title text-center mb-8">🔮 未来规划</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {upcomingFeatures.map((feature, index) => (
              <div 
                key={index}
                className="bg-gradient-to-br from-secondary-50 to-secondary-100 rounded-2xl p-6 border-2 border-dashed border-secondary-300"
              >
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-secondary-200 rounded-full flex items-center justify-center mx-auto mb-3">
                    <feature.icon className="w-6 h-6 text-text-secondary" />
                  </div>
                  <h3 className="font-semibold text-text-primary mb-2">{feature.title}</h3>
                  <span className="inline-block bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full text-xs font-medium">
                    {feature.status}
                  </span>
                </div>
                
                <p className="text-text-secondary text-sm mb-4 text-center">{feature.description}</p>
                
                <div className="space-y-2">
                  {feature.features.map((feat, featIndex) => (
                    <div key={featIndex} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-text-secondary rounded-full opacity-50"></div>
                      <span className="text-xs text-text-secondary">{feat}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Feature Comparison */}
        <div className="bg-white rounded-2xl p-8 shadow-lg mb-16">
          <h2 className="section-title mb-8">📊 功能对比</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-secondary-200">
                  <th className="text-left p-4 font-semibold text-text-primary">功能</th>
                  <th className="text-center p-4 font-semibold text-text-primary">基础版</th>
                  <th className="text-center p-4 font-semibold text-text-primary">当前版本</th>
                  <th className="text-center p-4 font-semibold text-text-primary">完整版</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-secondary-200">
                  <td className="p-4 font-medium">产品展示</td>
                  <td className="p-4 text-center">✅</td>
                  <td className="p-4 text-center">✅</td>
                  <td className="p-4 text-center">✅</td>
                </tr>
                <tr className="border-b border-secondary-200">
                  <td className="p-4 font-medium">购物车功能</td>
                  <td className="p-4 text-center">✅</td>
                  <td className="p-4 text-center">✅</td>
                  <td className="p-4 text-center">✅</td>
                </tr>
                <tr className="border-b border-secondary-200">
                  <td className="p-4 font-medium">搜索功能</td>
                  <td className="p-4 text-center">❌</td>
                  <td className="p-4 text-center">✅</td>
                  <td className="p-4 text-center">✅</td>
                </tr>
                <tr className="border-b border-secondary-200">
                  <td className="p-4 font-medium">愿望清单</td>
                  <td className="p-4 text-center">❌</td>
                  <td className="p-4 text-center">✅</td>
                  <td className="p-4 text-center">✅</td>
                </tr>
                <tr className="border-b border-secondary-200">
                  <td className="p-4 font-medium">产品比较</td>
                  <td className="p-4 text-center">❌</td>
                  <td className="p-4 text-center">✅</td>
                  <td className="p-4 text-center">✅</td>
                </tr>
                <tr className="border-b border-secondary-200">
                  <td className="p-4 font-medium">评价系统</td>
                  <td className="p-4 text-center">❌</td>
                  <td className="p-4 text-center">❌</td>
                  <td className="p-4 text-center">✅</td>
                </tr>
                <tr className="border-b border-secondary-200">
                  <td className="p-4 font-medium">优惠券系统</td>
                  <td className="p-4 text-center">❌</td>
                  <td className="p-4 text-center">❌</td>
                  <td className="p-4 text-center">✅</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <h2 className="section-title mb-6">🎉 体验新功能</h2>
          <p className="text-lg text-text-secondary mb-8 max-w-2xl mx-auto">
            所有新功能都已完成开发并集成到系统中，现在就开始体验这些强大的功能吧！
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <Link href="/us/search" className="btn-primary">
              🔍 搜索功能
            </Link>
            <Link href="/us/wishlist" className="btn-secondary">
              ❤️ 愿望清单
            </Link>
            <Link href="/us/compare" className="btn-accent">
              ⚖️ 产品比较
            </Link>
          </div>
          
          <div className="mt-8">
            <Link href="/us" className="text-primary hover:text-primary-600 font-medium">
              返回首页 →
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
