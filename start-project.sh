#!/bin/bash

echo "🚀 Starting Pet E-commerce Store..."

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start database and Redis services
echo "📦 Starting database and Redis services..."
docker-compose -f docker-compose.simple.yml up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Check if Medusa backend exists
if [ ! -d "medusa-backend" ]; then
    echo "🔧 Setting up Medusa backend for the first time..."
    ./setup-medusa.sh
fi

echo "🎯 Starting services..."

# Start Medusa backend in background
echo "🔥 Starting Medusa backend..."
cd medusa-backend
npx medusa develop &
MEDUSA_PID=$!
cd ..

# Wait for Medusa backend to start
echo "⏳ Waiting for Medusa backend to start..."
sleep 15

# Start Next.js frontend
echo "🌐 Starting Next.js frontend..."
yarn install
yarn dev &
NEXTJS_PID=$!

echo "✅ All services started!"
echo ""
echo "📋 Service URLs:"
echo "   🛍️  Storefront: http://localhost:8000"
echo "   🔧 Medusa Backend: http://localhost:9000"
echo "   👨‍💼 Admin Panel: http://localhost:7001"
echo "   🗄️  Database: localhost:5432"
echo ""
echo "🔑 Default admin credentials:"
echo "   Email: <EMAIL>"
echo "   Password: supersecret"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $MEDUSA_PID 2>/dev/null
    kill $NEXTJS_PID 2>/dev/null
    docker-compose -f docker-compose.simple.yml down
    echo "✅ All services stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
