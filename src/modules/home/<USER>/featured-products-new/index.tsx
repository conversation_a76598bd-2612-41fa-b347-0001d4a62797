'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { ChevronLeft, ChevronRight, ShoppingCart, Eye, Heart } from 'lucide-react'
import { HttpTypes } from "@medusajs/types"

interface FeaturedProductsProps {
  products: HttpTypes.StoreProduct[]
  region: HttpTypes.StoreRegion
  countryCode: string
}

const FeaturedProducts = ({ products, region, countryCode }: FeaturedProductsProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const [hoveredProduct, setHoveredProduct] = useState<string | null>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const sectionRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.2 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 320 // Width of one card plus gap
      const currentScroll = scrollContainerRef.current.scrollLeft
      const targetScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount

      scrollContainerRef.current.scrollTo({
        left: targetScroll,
        behavior: 'smooth'
      })
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: region.currency_code.toUpperCase()
    }).format(price / 100)
  }

  const getProductPrice = (product: HttpTypes.StoreProduct) => {
    const variant = product.variants?.[0]
    if (variant?.calculated_price) {
      return variant.calculated_price.calculated_amount
    }
    return 0
  }

  const getDiscountPercentage = (product: HttpTypes.StoreProduct) => {
    const metadata = product.metadata as any
    return metadata?.marketing?.discount ? parseInt(metadata.marketing.discount) : null
  }

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-b from-white to-secondary-50">
      <div className="content-container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`transition-all duration-600 ease-brand ${
              isVisible ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
            }`}
          >
            <span className="inline-block bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Featured Products
            </span>
            <h2 className="section-title">
              Premium Pet Products
            </h2>
            <p className="section-subtitle max-w-2xl mx-auto">
              Discover our carefully curated selection of premium pet products, designed to enhance your pet's life with cutting-edge technology and thoughtful design.
            </p>
          </div>
        </div>

        {/* Products Carousel */}
        <div className="relative">
          {/* Navigation Buttons */}
          <button
            onClick={() => scroll('left')}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-lg rounded-full p-3 transition-all duration-300 ease-brand hover:shadow-xl hover:scale-110 active:scale-95"
          >
            <ChevronLeft className="w-6 h-6 text-text-primary" />
          </button>
          
          <button
            onClick={() => scroll('right')}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-lg rounded-full p-3 transition-all duration-300 ease-brand hover:shadow-xl hover:scale-110 active:scale-95"
          >
            <ChevronRight className="w-6 h-6 text-text-primary" />
          </button>

          {/* Products Container */}
          <div
            ref={scrollContainerRef}
            className="flex gap-6 overflow-x-auto no-scrollbar px-12 py-4"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {products.map((product, index) => {
              const price = getProductPrice(product)
              const discount = getDiscountPercentage(product)
              const isHovered = hoveredProduct === product.id

              return (
                <div
                  key={product.id}
                  className={`flex-shrink-0 w-80 transition-all duration-600 ease-brand ${
                    isVisible ? 'animate-scale-in opacity-100' : 'opacity-0 scale-90'
                  }`}
                  style={{ animationDelay: `${index * 100}ms` }}
                  onMouseEnter={() => setHoveredProduct(product.id)}
                  onMouseLeave={() => setHoveredProduct(null)}
                >
                  <div className="bg-white rounded-2xl shadow-lg overflow-hidden card-hover group">
                    {/* Product Image */}
                    <div className="relative aspect-square overflow-hidden">
                      {discount && (
                        <div className="absolute top-4 left-4 z-10 bg-accent text-text-primary px-3 py-1 rounded-full text-sm font-bold">
                          -{discount}%
                        </div>
                      )}
                      
                      {product.thumbnail ? (
                        <img
                          src={product.thumbnail}
                          alt={product.title}
                          className="w-full h-full object-cover transition-transform duration-500 ease-brand group-hover:scale-110"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-500 text-sm">No Image</span>
                        </div>
                      )}
                      
                      {/* Overlay Actions */}
                      <div className={`absolute inset-0 bg-black/40 flex items-center justify-center gap-3 transition-all duration-300 ease-brand ${
                        isHovered ? 'opacity-100' : 'opacity-0'
                      }`}>
                        <Link
                          href={`/${countryCode}/products/${product.handle}`}
                          className="bg-white text-text-primary p-3 rounded-full transition-all duration-300 ease-brand hover:bg-primary hover:text-white hover:scale-110"
                        >
                          <Eye className="w-5 h-5" />
                        </Link>
                        
                        <button className="bg-white text-text-primary p-3 rounded-full transition-all duration-300 ease-brand hover:bg-accent hover:text-white hover:scale-110">
                          <ShoppingCart className="w-5 h-5" />
                        </button>
                        
                        <button className="bg-white text-text-primary p-3 rounded-full transition-all duration-300 ease-brand hover:bg-red-500 hover:text-white hover:scale-110">
                          <Heart className="w-5 h-5" />
                        </button>
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="p-6">
                      <h3 className="font-semibold text-lg text-text-primary mb-2 line-clamp-2 group-hover:text-primary transition-colors duration-300">
                        {product.title}
                      </h3>
                      
                      {product.description && (
                        <p className="text-text-secondary text-sm mb-4 line-clamp-2">
                          {product.description.replace(/<[^>]*>/g, '').substring(0, 100)}...
                        </p>
                      )}

                      {/* Price */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-xl font-bold text-text-primary">
                            {formatPrice(price)}
                          </span>
                          {discount && (
                            <span className="text-sm text-text-secondary line-through">
                              {formatPrice(Math.round(price / (1 - discount / 100)))}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <div key={i} className="w-3 h-3 text-accent">★</div>
                            ))}
                          </div>
                          <span className="text-xs text-text-secondary ml-1">(4.9)</span>
                        </div>
                      </div>

                      {/* Add to Cart Button */}
                      <button 
                        className={`w-full mt-4 btn-primary transition-all duration-300 ease-brand ${
                          isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
                        }`}
                      >
                        Add to Cart
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Link
            href={`/${countryCode}/store`}
            className="inline-flex items-center gap-2 btn-secondary hover:btn-primary transition-all duration-300 ease-brand"
          >
            View All Products
            <ChevronRight className="w-5 h-5" />
          </Link>
        </div>
      </div>
    </section>
  )
}

export default FeaturedProducts
