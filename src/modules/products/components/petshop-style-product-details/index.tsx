'use client'

import { HttpTypes } from "@medusajs/types"
import { useState } from "react"
import { Badge } from "@medusajs/ui"
import ProductActions from "../product-actions"

interface PetShopStyleProductDetailsProps {
  product: HttpTypes.StoreProduct
}

export default function PetShopStyleProductDetails({ product }: PetShopStyleProductDetailsProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedImage, setSelectedImage] = useState(0)
  const [selectedVariant, setSelectedVariant] = useState(product.variants?.[0])

  // Extract enhanced data from metadata
  const enhancedData = {
    features: product.metadata?.features as any[] || [],
    specifications: product.metadata?.specifications as any || {},
    faq: product.metadata?.faq as any[] || [],
    marketing: product.metadata?.marketing as any || {},
    warranty: product.metadata?.warranty as string || "24-Month Extended Warranty",
    shipping: product.metadata?.shipping as string || "Fast Shipping"
  }

  const tabs = [
    { id: "overview", label: "Overview" },
    { id: "specs", label: "Specs" },
    { id: "faq", label: "FAQ" },
    { id: "reviews", label: "Reviews" }
  ]

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price / 100)
  }

  const getDiscountPercentage = () => {
    if (enhancedData.marketing.discount) {
      return parseInt(enhancedData.marketing.discount)
    }
    return null
  }

  const getOriginalPrice = () => {
    if (selectedVariant?.prices?.[0]) {
      const currentPrice = selectedVariant.prices[0].amount
      const discount = getDiscountPercentage()
      if (discount) {
        return Math.round(currentPrice / (1 - discount / 100))
      }
    }
    return null
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Discount Badge */}
          {getDiscountPercentage() && (
            <div className="absolute z-10 top-4 left-4">
              <Badge variant="destructive" className="text-white bg-red-500">
                -{getDiscountPercentage()}%
              </Badge>
            </div>
          )}
          
          {/* Main Image */}
          <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
            <img
              src={product.images?.[selectedImage]?.url || product.thumbnail || '/placeholder.png'}
              alt={product.title}
              className="w-full h-full object-cover"
            />
          </div>
          
          {/* Image Thumbnails */}
          <div className="grid grid-cols-6 gap-2">
            {product.images?.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                className={`aspect-square rounded-md overflow-hidden border-2 ${
                  selectedImage === index ? 'border-blue-500' : 'border-gray-200'
                }`}
              >
                <img
                  src={image.url}
                  alt={`${product.title} ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{product.title}</h1>
            {product.subtitle && (
              <p className="text-lg text-gray-600 mt-2">{product.subtitle}</p>
            )}
          </div>

          {/* Reviews */}
          <div className="flex items-center space-x-2">
            <div className="flex text-yellow-400">
              {[...Array(5)].map((_, i) => (
                <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                  <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                </svg>
              ))}
            </div>
            <span className="text-sm text-gray-600">23 Reviews</span>
          </div>

          {/* Price */}
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <span className="text-3xl font-bold text-gray-900">
                {selectedVariant?.prices?.[0] && formatPrice(selectedVariant.prices[0].amount)}
              </span>
              {getOriginalPrice() && (
                <span className="text-xl text-gray-500 line-through">
                  {formatPrice(getOriginalPrice()!)}
                </span>
              )}
              {getDiscountPercentage() && (
                <Badge variant="destructive">{getDiscountPercentage()}% OFF</Badge>
              )}
            </div>
          </div>

          {/* Variant Selection */}
          {product.options?.map((option) => (
            <div key={option.id} className="space-y-2">
              <label className="text-sm font-medium text-gray-900">{option.title}:</label>
              <div className="flex space-x-2">
                {option.values?.map((value) => (
                  <button
                    key={value.id}
                    className={`px-4 py-2 border rounded-md text-sm font-medium ${
                      selectedVariant?.options?.find(o => o.option_id === option.id)?.value === value.value
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-300 text-gray-700 hover:border-gray-400'
                    }`}
                    onClick={() => {
                      const variant = product.variants?.find(v => 
                        v.options?.some(o => o.option_id === option.id && o.value === value.value)
                      )
                      if (variant) setSelectedVariant(variant)
                    }}
                  >
                    {value.value}
                  </button>
                ))}
              </div>
            </div>
          ))}

          {/* Add to Cart */}
          <div className="space-y-4">
            <ProductActions
              product={product}
              region={{
                id: 'reg_01K1D1N9TAK3B8XRDHE7H7CFEN',
                name: 'USA',
                currency_code: 'usd'
              } as any}
            />
          </div>

          {/* Payment Methods */}
          <div className="flex flex-wrap gap-2">
            {['PayPal', 'Google Pay', 'Apple Pay', 'Visa', 'Mastercard'].map((method) => (
              <div key={method} className="px-3 py-1 bg-gray-100 rounded text-xs font-medium text-gray-600">
                {method}
              </div>
            ))}
          </div>

          {/* Trust Badges */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>{enhancedData.shipping}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>{enhancedData.warranty}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>30-day Money Back</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>24/7 Customer Support</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Section */}
      <div className="mt-16">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="py-8">
          {activeTab === "overview" && (
            <div className="space-y-8">
              {/* Product Description */}
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: product.description || '' }} />
              </div>

              {/* Features Grid */}
              {enhancedData.features.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {enhancedData.features.map((feature, index) => (
                    <div key={index} className="text-center space-y-3">
                      <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-2xl">{feature.icon || '📱'}</span>
                      </div>
                      <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                      <p className="text-sm text-gray-600">{feature.description}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === "specs" && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold">Specifications</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(enhancedData.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b border-gray-200">
                    <span className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                    <span className="text-gray-600">{value as string}</span>
                  </div>
                ))}
                
                {/* Default specs from product */}
                <div className="flex justify-between py-2 border-b border-gray-200">
                  <span className="font-medium">Weight</span>
                  <span className="text-gray-600">{product.weight ? `${product.weight}g` : 'N/A'}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-200">
                  <span className="font-medium">Dimensions</span>
                  <span className="text-gray-600">
                    {product.length && product.width && product.height 
                      ? `${product.length} × ${product.width} × ${product.height} cm`
                      : 'N/A'
                    }
                  </span>
                </div>
              </div>
            </div>
          )}

          {activeTab === "faq" && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold">FAQ</h3>
              <div className="space-y-4">
                {enhancedData.faq.map((item, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Q: {item.question}</h4>
                    <p className="text-gray-600">A: {item.answer}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === "reviews" && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold">Customer Reviews</h3>
              <div className="text-center py-8 text-gray-500">
                <p>Reviews functionality would be implemented here</p>
                <p className="text-sm">Integration with review system needed</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
