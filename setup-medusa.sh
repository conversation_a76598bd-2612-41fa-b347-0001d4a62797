#!/bin/bash

echo "Setting up Medusa backend..."

# Create medusa backend directory if it doesn't exist
if [ ! -d "medusa-backend" ]; then
    echo "Creating Medusa backend..."
    npx create-medusa-app@latest medusa-backend --db-url postgres://medusa_user:medusa_password@localhost:5433/medusa_db --no-browser
fi

cd medusa-backend

echo "Installing dependencies..."
npm install

echo "Running migrations..."
npx medusa migrations run

echo "Seeding database..."
npx medusa seed -f ./data/seed.json || echo "No seed file found, creating admin user..."

echo "Creating admin user..."
npx medusa user -e <EMAIL> -p supersecret || echo "Admin user might already exist"

echo "Medusa backend setup complete!"
echo "You can start the backend with: cd medusa-backend && npx medusa develop"
