import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

type SortOption = 'created_at' | 'price_asc' | 'price_desc' | 'title_asc' | 'title_desc'

interface PriceRange {
  min: number
  max: number
}

interface ProductFilterState {
  // Search and filters
  searchQuery: string
  selectedCategories: string[]
  selectedTags: string[]
  priceRange: PriceRange
  sortBy: SortOption
  inStock: boolean
  onSale: boolean
  
  // Pagination
  currentPage: number
  itemsPerPage: number
  
  // UI state
  isFilterOpen: boolean
  isLoading: boolean
  
  // Actions
  setSearchQuery: (query: string) => void
  addCategory: (categoryId: string) => void
  removeCategory: (categoryId: string) => void
  toggleCategory: (categoryId: string) => void
  clearCategories: () => void
  
  addTag: (tag: string) => void
  removeTag: (tag: string) => void
  toggleTag: (tag: string) => void
  clearTags: () => void
  
  setPriceRange: (range: PriceRange) => void
  setSortBy: (sort: SortOption) => void
  toggleInStock: () => void
  toggleOnSale: () => void
  
  setCurrentPage: (page: number) => void
  setItemsPerPage: (count: number) => void
  
  toggleFilter: () => void
  openFilter: () => void
  closeFilter: () => void
  setLoading: (loading: boolean) => void
  
  clearAllFilters: () => void
  
  // Computed
  hasActiveFilters: () => boolean
  getFilterCount: () => number
}

export const useProductFilterStore = create<ProductFilterState>()(
  devtools(
    (set, get) => ({
      // Initial state
      searchQuery: '',
      selectedCategories: [],
      selectedTags: [],
      priceRange: { min: 0, max: 1000 },
      sortBy: 'created_at',
      inStock: false,
      onSale: false,
      currentPage: 1,
      itemsPerPage: 12,
      isFilterOpen: false,
      isLoading: false,

      // Search actions
      setSearchQuery: (query) => {
        set({ searchQuery: query, currentPage: 1 }) // Reset to first page on search
      },

      // Category actions
      addCategory: (categoryId) => {
        set((state) => {
          if (!state.selectedCategories.includes(categoryId)) {
            return { 
              selectedCategories: [...state.selectedCategories, categoryId],
              currentPage: 1 
            }
          }
          return state
        })
      },

      removeCategory: (categoryId) => {
        set((state) => ({
          selectedCategories: state.selectedCategories.filter(id => id !== categoryId),
          currentPage: 1
        }))
      },

      toggleCategory: (categoryId) => {
        const state = get()
        if (state.selectedCategories.includes(categoryId)) {
          state.removeCategory(categoryId)
        } else {
          state.addCategory(categoryId)
        }
      },

      clearCategories: () => {
        set({ selectedCategories: [], currentPage: 1 })
      },

      // Tag actions
      addTag: (tag) => {
        set((state) => {
          if (!state.selectedTags.includes(tag)) {
            return { 
              selectedTags: [...state.selectedTags, tag],
              currentPage: 1 
            }
          }
          return state
        })
      },

      removeTag: (tag) => {
        set((state) => ({
          selectedTags: state.selectedTags.filter(t => t !== tag),
          currentPage: 1
        }))
      },

      toggleTag: (tag) => {
        const state = get()
        if (state.selectedTags.includes(tag)) {
          state.removeTag(tag)
        } else {
          state.addTag(tag)
        }
      },

      clearTags: () => {
        set({ selectedTags: [], currentPage: 1 })
      },

      // Price and sort actions
      setPriceRange: (range) => {
        set({ priceRange: range, currentPage: 1 })
      },

      setSortBy: (sort) => {
        set({ sortBy: sort, currentPage: 1 })
      },

      toggleInStock: () => {
        set((state) => ({ inStock: !state.inStock, currentPage: 1 }))
      },

      toggleOnSale: () => {
        set((state) => ({ onSale: !state.onSale, currentPage: 1 }))
      },

      // Pagination actions
      setCurrentPage: (page) => {
        set({ currentPage: page })
      },

      setItemsPerPage: (count) => {
        set({ itemsPerPage: count, currentPage: 1 })
      },

      // UI actions
      toggleFilter: () => {
        set((state) => ({ isFilterOpen: !state.isFilterOpen }))
      },

      openFilter: () => {
        set({ isFilterOpen: true })
      },

      closeFilter: () => {
        set({ isFilterOpen: false })
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      // Clear all filters
      clearAllFilters: () => {
        set({
          searchQuery: '',
          selectedCategories: [],
          selectedTags: [],
          priceRange: { min: 0, max: 1000 },
          sortBy: 'created_at',
          inStock: false,
          onSale: false,
          currentPage: 1
        })
      },

      // Computed values
      hasActiveFilters: () => {
        const state = get()
        return (
          state.searchQuery !== '' ||
          state.selectedCategories.length > 0 ||
          state.selectedTags.length > 0 ||
          state.priceRange.min > 0 ||
          state.priceRange.max < 1000 ||
          state.inStock ||
          state.onSale
        )
      },

      getFilterCount: () => {
        const state = get()
        let count = 0
        
        if (state.searchQuery !== '') count++
        if (state.selectedCategories.length > 0) count++
        if (state.selectedTags.length > 0) count++
        if (state.priceRange.min > 0 || state.priceRange.max < 1000) count++
        if (state.inStock) count++
        if (state.onSale) count++
        
        return count
      }
    }),
    {
      name: 'product-filter-store',
    }
  )
)
