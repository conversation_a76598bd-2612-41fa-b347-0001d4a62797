import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { HttpTypes } from '@medusajs/types'

interface CartItem {
  id: string
  title: string
  thumbnail?: string
  variant_id: string
  quantity: number
  unit_price: number
  total: number
}

interface CartState {
  // State
  items: CartItem[]
  isOpen: boolean
  isLoading: boolean
  cartId: string | null
  itemCount: number
  subtotal: number
  
  // Actions
  addItem: (item: Omit<CartItem, 'total'>) => void
  removeItem: (variantId: string) => void
  updateQuantity: (variantId: string, quantity: number) => void
  clearCart: () => void
  toggleCart: () => void
  openCart: () => void
  closeCart: () => void
  setLoading: (loading: boolean) => void
  setCartId: (cartId: string) => void
  
  // Computed values
  getItemCount: () => number
  getSubtotal: () => number
}

export const useCartStore = create<CartState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        items: [],
        isOpen: false,
        isLoading: false,
        cartId: null,
        itemCount: 0,
        subtotal: 0,

        // Actions
        addItem: (newItem) => {
          set((state) => {
            const existingItemIndex = state.items.findIndex(
              item => item.variant_id === newItem.variant_id
            )

            let updatedItems: CartItem[]

            if (existingItemIndex >= 0) {
              // Update existing item
              updatedItems = state.items.map((item, index) => 
                index === existingItemIndex 
                  ? { 
                      ...item, 
                      quantity: item.quantity + newItem.quantity,
                      total: (item.quantity + newItem.quantity) * item.unit_price
                    }
                  : item
              )
            } else {
              // Add new item
              const itemWithTotal = {
                ...newItem,
                total: newItem.quantity * newItem.unit_price
              }
              updatedItems = [...state.items, itemWithTotal]
            }

            const itemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)
            const subtotal = updatedItems.reduce((sum, item) => sum + item.total, 0)

            return {
              items: updatedItems,
              itemCount,
              subtotal,
              isOpen: true // Auto-open cart when item is added
            }
          })
        },

        removeItem: (variantId) => {
          set((state) => {
            const updatedItems = state.items.filter(item => item.variant_id !== variantId)
            const itemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)
            const subtotal = updatedItems.reduce((sum, item) => sum + item.total, 0)

            return {
              items: updatedItems,
              itemCount,
              subtotal
            }
          })
        },

        updateQuantity: (variantId, quantity) => {
          if (quantity <= 0) {
            get().removeItem(variantId)
            return
          }

          set((state) => {
            const updatedItems = state.items.map(item => 
              item.variant_id === variantId 
                ? { 
                    ...item, 
                    quantity,
                    total: quantity * item.unit_price
                  }
                : item
            )

            const itemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)
            const subtotal = updatedItems.reduce((sum, item) => sum + item.total, 0)

            return {
              items: updatedItems,
              itemCount,
              subtotal
            }
          })
        },

        clearCart: () => {
          set({
            items: [],
            itemCount: 0,
            subtotal: 0,
            cartId: null
          })
        },

        toggleCart: () => {
          set((state) => ({ isOpen: !state.isOpen }))
        },

        openCart: () => {
          set({ isOpen: true })
        },

        closeCart: () => {
          set({ isOpen: false })
        },

        setLoading: (loading) => {
          set({ isLoading: loading })
        },

        setCartId: (cartId) => {
          set({ cartId })
        },

        // Computed values
        getItemCount: () => {
          const state = get()
          return state.items.reduce((sum, item) => sum + item.quantity, 0)
        },

        getSubtotal: () => {
          const state = get()
          return state.items.reduce((sum, item) => sum + item.total, 0)
        }
      }),
      {
        name: 'pet-store-cart', // localStorage key
        partialize: (state) => ({ 
          items: state.items, 
          cartId: state.cartId,
          itemCount: state.itemCount,
          subtotal: state.subtotal
        }), // Only persist these fields
      }
    ),
    {
      name: 'cart-store', // DevTools name
    }
  )
)
