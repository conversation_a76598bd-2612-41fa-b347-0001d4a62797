import { listProductsWithSort } from "@lib/data/products"
import { NextRequest, NextResponse } from "next/server"
import { SortOptions } from "@modules/store/components/refinement-list/sort-products"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const countryCode = searchParams.get("countryCode")
    const sortBy = (searchParams.get("sortBy") || "created_at") as SortOptions
    const collectionId = searchParams.get("collectionId")
    const categoryId = searchParams.get("categoryId")
    const productsIds = searchParams.get("productsIds")?.split(",")

    if (!countryCode) {
      return NextResponse.json(
        { error: "Country code is required" },
        { status: 400 }
      )
    }

    const queryParams: any = {
      limit: 12,
    }

    if (collectionId) {
      queryParams["collection_id"] = [collectionId]
    }

    if (categoryId) {
      queryParams["category_id"] = [categoryId]
    }

    if (productsIds) {
      queryParams["id"] = productsIds
    }

    if (sortBy === "created_at") {
      queryParams["order"] = "created_at"
    }

    const {
      response: { products, count },
    } = await listProductsWithSort({
      page,
      queryParams,
      sortBy,
      countryCode,
    })

    return NextResponse.json({ products, count })
  } catch (error) {
    console.error("Error fetching products:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
