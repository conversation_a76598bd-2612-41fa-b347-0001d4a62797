version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: medusa_postgres
    environment:
      POSTGRES_USER: medusa_user
      POSTGRES_PASSWORD: medusa_password
      POSTGRES_DB: medusa_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - medusa_network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: medusa_redis
    ports:
      - "6379:6379"
    networks:
      - medusa_network

  # Medusa Backend
  medusa_backend:
    image: medusajs/medusa:latest
    container_name: medusa_backend
    depends_on:
      - postgres
      - redis
    environment:
      DATABASE_URL: ****************************************************/medusa_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET: some_jwt_secret
      COOKIE_SECRET: some_cookie_secret
      NODE_ENV: development
      MEDUSA_ADMIN_ONBOARDING_TYPE: default
      MEDUSA_ADMIN_ONBOARDING_NEXTJS: true
    ports:
      - "9000:9000"
      - "7001:7001"  # Admin panel
    volumes:
      - medusa_uploads:/app/uploads
    networks:
      - medusa_network
    command: >
      sh -c "
        npx medusa migrations run &&
        npx medusa seed -f ./data/seed.json &&
        npx medusa develop
      "

  # Next.js Frontend (Storefront)
  nextjs_storefront:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nextjs_storefront
    depends_on:
      - medusa_backend
    environment:
      MEDUSA_BACKEND_URL: http://medusa_backend:9000
      NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY: pk_test
      NEXT_PUBLIC_BASE_URL: http://localhost:8000
      NEXT_PUBLIC_DEFAULT_REGION: us
      REVALIDATE_SECRET: supersecret
      NODE_ENV: production
    ports:
      - "8000:8000"
    networks:
      - medusa_network

volumes:
  postgres_data:
  medusa_uploads:

networks:
  medusa_network:
    driver: bridge
