'use client'

import { useState, useRef, useEffect } from 'react'
import { Heart, Shield, Award, Users } from 'lucide-react'

const BrandStory = () => {
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.3 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const stats = [
    {
      icon: Heart,
      number: "10,000+",
      label: "Happy Pets",
      color: "text-red-500"
    },
    {
      icon: Shield,
      number: "99.9%",
      label: "Reliability",
      color: "text-green-500"
    },
    {
      icon: Award,
      number: "50+",
      label: "Awards Won",
      color: "text-accent"
    },
    {
      icon: Users,
      number: "5,000+",
      label: "Pet Parents",
      color: "text-primary"
    }
  ]

  return (
    <section ref={sectionRef} className="py-20 bg-white overflow-hidden">
      <div className="content-container">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Image */}
          <div 
            className={`relative transition-all duration-800 ease-brand ${
              isVisible ? 'animate-slide-in-left opacity-100' : 'opacity-0 -translate-x-12'
            }`}
          >
            <div className="relative">
              {/* Main Image */}
              <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Happy pet with smart feeder"
                  className="w-full h-[500px] object-cover"
                />
              </div>
              
              {/* Floating Card */}
              <div className="absolute -bottom-8 -right-8 bg-white rounded-xl shadow-xl p-6 z-20 max-w-xs">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    <Heart className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <div className="font-bold text-lg text-text-primary">4.9/5</div>
                    <div className="text-sm text-text-secondary">Customer Rating</div>
                  </div>
                </div>
                <div className="text-sm text-text-secondary">
                  "The best investment I've made for my pet's health and happiness!"
                </div>
              </div>

              {/* Background Decoration */}
              <div className="absolute -top-8 -left-8 w-32 h-32 bg-primary-100 rounded-full opacity-50 -z-10"></div>
              <div className="absolute -bottom-4 -left-4 w-24 h-24 bg-accent-100 rounded-full opacity-50 -z-10"></div>
            </div>
          </div>

          {/* Right Side - Content */}
          <div 
            className={`transition-all duration-800 ease-brand ${
              isVisible ? 'animate-slide-in-right opacity-100' : 'opacity-0 translate-x-12'
            }`}
            style={{ animationDelay: '200ms' }}
          >
            <div className="space-y-6">
              {/* Badge */}
              <div className="inline-flex items-center gap-2 bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-semibold">
                <Heart className="w-4 h-4" />
                Our Story
              </div>

              {/* Title */}
              <h2 className="section-title">
                Crafted with Love,
                <span className="text-gradient block">Built for Life</span>
              </h2>

              {/* Description */}
              <div className="space-y-4 text-text-secondary leading-relaxed">
                <p>
                  Founded by passionate pet parents, we understand the deep bond between you and your furry companions. Our mission is to enhance that relationship through innovative technology that brings peace of mind and joy to both pets and their families.
                </p>
                <p>
                  Every product in our collection is meticulously designed and rigorously tested to ensure it meets the highest standards of quality, safety, and functionality. We believe that your pets deserve nothing less than the best.
                </p>
              </div>

              {/* Features */}
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-text-primary mb-1">Smart Technology</h4>
                    <p className="text-text-secondary text-sm">AI-powered solutions that adapt to your pet's unique needs and behaviors.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-text-primary mb-1">Premium Materials</h4>
                    <p className="text-text-secondary text-sm">Food-grade, pet-safe materials that ensure durability and safety.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-text-primary mb-1">24/7 Support</h4>
                    <p className="text-text-secondary text-sm">Dedicated customer support team ready to help you and your pets.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20">
          <div 
            className={`grid grid-cols-2 lg:grid-cols-4 gap-8 transition-all duration-800 ease-brand ${
              isVisible ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
            }`}
            style={{ animationDelay: '600ms' }}
          >
            {stats.map((stat, index) => (
              <div 
                key={index}
                className="text-center group"
                style={{ animationDelay: `${600 + index * 100}ms` }}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary-100 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 ease-brand">
                  <stat.icon className={`w-8 h-8 ${stat.color}`} />
                </div>
                <div className="text-3xl font-bold text-text-primary mb-2">
                  {stat.number}
                </div>
                <div className="text-text-secondary font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default BrandStory
