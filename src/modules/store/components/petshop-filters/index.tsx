'use client'

import { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'

interface FilterSection {
  title: string
  options: string[]
  isOpen: boolean
}

const PetShopFilters = () => {
  const [filters, setFilters] = useState<FilterSection[]>([
    {
      title: 'Series',
      options: ['Cube', 'Heritage', 'Castle', 'Pioneer', 'Barn', 'Guardian', 'Fountain Accessories', 'Feeder Accessories'],
      isOpen: true
    },
    {
      title: 'Feature',
      options: ['App Control', 'Camera Monitoring', 'Large Capacity', 'Plastic', 'Stainless Steel', 'Dual Food Tray', 'Basic'],
      isOpen: true
    },
    {
      title: 'Capacity',
      options: ['7L', '6L', '5L', '4L', '2L-3L'],
      isOpen: true
    },
    {
      title: 'Price',
      options: ['Under $30', '$30-$50', '$50-$80', 'Over $80'],
      isOpen: true
    }
  ])

  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({})

  const toggleSection = (index: number) => {
    setFilters(prev => prev.map((filter, i) => 
      i === index ? { ...filter, isOpen: !filter.isOpen } : filter
    ))
  }

  const handleFilterChange = (section: string, option: string, checked: boolean) => {
    setSelectedFilters(prev => {
      const sectionFilters = prev[section] || []
      if (checked) {
        return {
          ...prev,
          [section]: [...sectionFilters, option]
        }
      } else {
        return {
          ...prev,
          [section]: sectionFilters.filter(f => f !== option)
        }
      }
    })
  }

  const clearAllFilters = () => {
    setSelectedFilters({})
  }

  const hasActiveFilters = Object.values(selectedFilters).some(filters => filters.length > 0)

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Filter</h2>
        {hasActiveFilters && (
          <button 
            onClick={clearAllFilters}
            className="text-orange-500 hover:text-orange-600 text-sm font-medium"
          >
            Clear
          </button>
        )}
      </div>

      {/* Filter Sections */}
      <div className="space-y-6">
        {filters.map((filter, index) => (
          <div key={filter.title} className="border-b border-gray-100 pb-6 last:border-b-0 last:pb-0">
            {/* Section Header */}
            <button
              onClick={() => toggleSection(index)}
              className="flex items-center justify-between w-full text-left"
            >
              <h3 className="font-semibold text-gray-900">{filter.title}</h3>
              {filter.isOpen ? (
                <ChevronUp className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              )}
            </button>

            {/* Section Options */}
            {filter.isOpen && (
              <div className="mt-3 space-y-2">
                {filter.options.map((option) => {
                  const isSelected = selectedFilters[filter.title]?.includes(option) || false
                  return (
                    <label key={option} className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => handleFilterChange(filter.title, option, e.target.checked)}
                        className="rounded border-gray-300 text-orange-500 focus:ring-orange-500 focus:ring-offset-0"
                      />
                      <span className="ml-2 text-sm text-gray-600 hover:text-gray-900">
                        {option}
                      </span>
                    </label>
                  )
                })}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Apply Button */}
      <div className="mt-6 pt-6 border-t border-gray-100">
        <button className="w-full bg-orange-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-orange-600 transition-colors duration-200">
          Filter
        </button>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Active Filters:</h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(selectedFilters).map(([section, options]) =>
              options.map((option) => (
                <span
                  key={`${section}-${option}`}
                  className="inline-flex items-center gap-1 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded"
                >
                  {option}
                  <button
                    onClick={() => handleFilterChange(section, option, false)}
                    className="hover:text-orange-900"
                  >
                    ×
                  </button>
                </span>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default PetShopFilters
