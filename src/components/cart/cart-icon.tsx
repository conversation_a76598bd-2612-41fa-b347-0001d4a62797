'use client'

import { useCartStore } from '../../lib/stores'
import { ShoppingBagIcon } from '@heroicons/react/24/outline'

export default function CartIcon() {
  const { itemCount, openCart } = useCartStore()

  return (
    <button
      onClick={openCart}
      className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
      aria-label={`Shopping cart with ${itemCount} items`}
    >
      <ShoppingBagIcon className="h-6 w-6" />
      
      {/* Item count badge */}
      {itemCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center min-w-[1.25rem]">
          {itemCount > 99 ? '99+' : itemCount}
        </span>
      )}
    </button>
  )
}
