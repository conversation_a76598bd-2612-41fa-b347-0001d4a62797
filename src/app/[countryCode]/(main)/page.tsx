import { Metadata } from "next"
import { getRegion } from "@lib/data/regions"
import { listProducts } from "@lib/data/products"
import HeroSection from "@modules/home/<USER>/hero-section"
import FeaturedProducts from "@modules/home/<USER>/featured-products-new"
import BrandStory from "@modules/home/<USER>/brand-story"
import Testimonials from "@modules/home/<USER>/testimonials"

export const metadata: Metadata = {
  title: "Pet Store - Premium Pet Products & Accessories",
  description: "Discover premium pet products designed with love. From smart feeders to interactive toys, we provide everything your furry friends need for a happy, healthy life.",
  openGraph: {
    title: "Pet Store - Premium Pet Products & Accessories",
    description: "Discover premium pet products designed with love. From smart feeders to interactive toys, we provide everything your furry friends need for a happy, healthy life.",
    type: "website",
  },
}

type Props = {
  params: Promise<{ countryCode: string }>
}

export default async function HomePage(props: Props) {
  const params = await props.params
  const { countryCode } = params

  // Get region for the country
  const region = await getRegion(countryCode)

  if (!region) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-text-primary mb-4">
            Region not found
          </h1>
          <p className="text-text-secondary">
            The region for country code "{countryCode}" could not be found.
          </p>
        </div>
      </div>
    )
  }

  // Get featured products
  const { response: productsResponse } = await listProducts({
    countryCode,
    queryParams: {
      limit: 8,
      fields: "*variants.calculated_price,+variants.inventory_quantity,+metadata,+tags"
    },
  })

  const featuredProducts = productsResponse.products

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />

      {/* Featured Products */}
      <FeaturedProducts
        products={featuredProducts}
        region={region}
        countryCode={countryCode}
      />

      {/* Brand Story */}
      <BrandStory />

      {/* Testimonials */}
      <Testimonials />
    </main>
  )
}
