"use server"

import { sdk } from "@lib/config"
import { sortProducts } from "@lib/util/sort-products"
import { HttpTypes } from "@medusajs/types"
import { SortOptions } from "@modules/store/components/refinement-list/sort-products"
import { getAuthHeaders, getCacheOptions } from "./cookies"
import { getRegion, retrieveRegion } from "./regions"

export const listProducts = async ({
  pageParam = 1,
  queryParams,
  countryCode,
  regionId,
}: {
  pageParam?: number
  queryParams?: HttpTypes.FindParams & HttpTypes.StoreProductParams
  countryCode?: string
  regionId?: string
}): Promise<{
  response: { products: HttpTypes.StoreProduct[]; count: number }
  nextPage: number | null
  queryParams?: HttpTypes.FindParams & HttpTypes.StoreProductParams
}> => {
  if (!countryCode && !regionId) {
    throw new Error("Country code or region ID is required")
  }

  const limit = queryParams?.limit || 12
  const _pageParam = Math.max(pageParam, 1)
  const offset = (_pageParam === 1) ? 0 : (_pageParam - 1) * limit;

  let region: HttpTypes.StoreRegion | undefined | null

  if (countryCode) {
    region = await getRegion(countryCode)
  } else {
    region = await retrieveRegion(regionId!)
  }

  if (!region) {
    return {
      response: { products: [], count: 0 },
      nextPage: null,
    }
  }

  const headers = {
    ...(await getAuthHeaders()),
  }

  const next = {
    ...(await getCacheOptions("products")),
  }

  return sdk.client
    .fetch<{ products: HttpTypes.StoreProduct[]; count: number }>(
      `/store/products`,
      {
        method: "GET",
        query: {
          limit,
          offset,
          region_id: region?.id,
          fields:
            "*variants.calculated_price,+variants.inventory_quantity,+metadata,+tags",
          ...queryParams,
        },
        headers,
        next,
        cache: "force-cache",
      }
    )
    .then(({ products, count }) => {
      const nextPage = count > offset + limit ? pageParam + 1 : null

      return {
        response: {
          products,
          count,
        },
        nextPage: nextPage,
        queryParams,
      }
    })
}

/**
 * This will fetch 100 products to the Next.js cache and sort them based on the sortBy parameter.
 * It will then return the paginated products based on the page and limit parameters.
 */
export const listProductsWithSort = async ({
  page = 0,
  queryParams,
  sortBy = "created_at",
  countryCode,
}: {
  page?: number
  queryParams?: HttpTypes.FindParams & HttpTypes.StoreProductParams
  sortBy?: SortOptions
  countryCode: string
}): Promise<{
  response: { products: HttpTypes.StoreProduct[]; count: number }
  nextPage: number | null
  queryParams?: HttpTypes.FindParams & HttpTypes.StoreProductParams
}> => {
  const limit = queryParams?.limit || 12

  // 如果有categoryId，我们需要获取所有产品然后在前端过滤
  // 因为产品没有正确分配到categories
  const backendQueryParams = { ...queryParams }
  if (backendQueryParams.category_id) {
    delete backendQueryParams.category_id
  }

  const {
    response: { products, count },
  } = await listProducts({
    pageParam: 1,
    queryParams: {
      ...backendQueryParams,
      limit: 100,
    },
    countryCode,
  })

  // 临时的category过滤逻辑（基于产品标题）
  let filteredProducts = products
  const categoryId = queryParams.category_id ? queryParams.category_id[0] : null

  if (categoryId) {
    filteredProducts = products.filter(product => {
      const title = product.title.toLowerCase()

      switch (categoryId) {
        case 'automatic-feeder':
          return title.includes('feeder') || title.includes('feeding')
        case 'water-pet-fountain':
          return title.includes('fountain') || title.includes('water')
        case 'toys':
          return title.includes('toy') || title.includes('interactive') || title.includes('play')
        case 'accessories':
          return title.includes('collar') || title.includes('carrier') || title.includes('storage') ||
                 title.includes('grooming') || title.includes('bed') || title.includes('door') ||
                 title.includes('tracker') || title.includes('camera') || title.includes('wheel')
        default:
          return false
      }
    })
  }

  const sortedProducts = sortProducts(filteredProducts, sortBy)
  const filteredCount = filteredProducts.length

  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit

  const nextPage = filteredCount > endIndex ? page + 1 : null

  const paginatedProducts = sortedProducts.slice(startIndex, endIndex)

  return {
    response: {
      products: paginatedProducts,
      count: filteredCount,
    },
    nextPage,
    queryParams,
  }
}

// Get products by IDs
export const getProductsById = async (
  productHandles: string[],
  countryCode: string = 'us'
): Promise<HttpTypes.StoreProduct[]> => {
  if (productHandles.length === 0) {
    return []
  }

  try {
    const products = await Promise.all(
      productHandles.map(async (handle) => {
        try {
          const { response } = await listProducts({
            countryCode,
            queryParams: { handle, limit: 1 }
          })
          return response.products[0] || null
        } catch (error) {
          console.warn(`Product with handle ${handle} not found`)
          return null
        }
      })
    )

    return products.filter((product): product is HttpTypes.StoreProduct => product !== null)
  } catch (error) {
    console.error('Error fetching products by handles:', error)
    return []
  }
}
