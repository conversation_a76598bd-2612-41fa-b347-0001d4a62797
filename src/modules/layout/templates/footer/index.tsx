import { listCategories } from "@lib/data/categories"
import { listCollections } from "@lib/data/collections"
import { Text, clx } from "@medusajs/ui"
import { Heart, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube, ArrowRight } from "lucide-react"

import LocalizedClientLink from "@modules/common/components/localized-client-link"
import MedusaCTA from "@modules/layout/components/medusa-cta"

export default async function Footer() {
  const { collections } = await listCollections({
    fields: "*products",
  })
  const productCategories = await listCategories()

  return (
    <footer className="bg-gradient-to-br from-text-primary via-gray-800 to-gray-900 text-white">
      {/* Newsletter Section */}
      <div className="bg-gradient-to-r from-primary to-accent py-16">
        <div className="content-container">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-3xl font-bold text-white mb-4">
              Stay Connected with Your Pet's Health
            </h3>
            <p className="text-white/90 text-lg mb-8">
              Get exclusive tips, product updates, and special offers delivered to your inbox
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <div className="flex-1 relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full pl-10 pr-4 py-3 rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-white/50"
                />
              </div>
              <button className="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300 flex items-center gap-2">
                Subscribe
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="content-container py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <LocalizedClientLink
              href="/"
              className="flex items-center gap-3 mb-6 group"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center transition-transform duration-300 group-hover:scale-110">
                <Heart className="w-7 h-7 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">PetStore</div>
                <div className="text-sm text-gray-400">Premium Pet Care</div>
              </div>
            </LocalizedClientLink>
            <p className="text-gray-300 mb-6 leading-relaxed">
              We're passionate about enhancing the bond between pets and their families through innovative, high-quality products designed with love and care.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-gray-300">
                <Phone className="w-4 h-4 text-primary" />
                <span className="text-sm">+****************</span>
              </div>
              <div className="flex items-center gap-3 text-gray-300">
                <Mail className="w-4 h-4 text-primary" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-gray-300">
                <MapPin className="w-4 h-4 text-primary" />
                <span className="text-sm">123 Pet Street, Animal City, AC 12345</span>
              </div>
            </div>
          </div>
          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-6">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <LocalizedClientLink
                  href="/store"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  All Products
                </LocalizedClientLink>
              </li>
              <li>
                <LocalizedClientLink
                  href="/store?category=feeders"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  Smart Feeders
                </LocalizedClientLink>
              </li>
              <li>
                <LocalizedClientLink
                  href="/store?category=toys"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  Interactive Toys
                </LocalizedClientLink>
              </li>
              <li>
                <LocalizedClientLink
                  href="/store?category=accessories"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  Accessories
                </LocalizedClientLink>
              </li>
            </ul>
          </div>

          {/* Categories */}
          {productCategories && productCategories?.length > 0 && (
            <div>
              <h4 className="text-lg font-semibold text-white mb-6">Categories</h4>
              <ul className="space-y-3" data-testid="footer-categories">
                {productCategories?.slice(0, 6).map((c) => {
                  if (c.parent_category) {
                    return null
                  }

                  return (
                    <li key={c.id}>
                      <LocalizedClientLink
                        className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                        href={`/categories/${c.handle}`}
                        data-testid="category-link"
                      >
                        <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                        {c.name}
                      </LocalizedClientLink>
                    </li>
                  )
                })}
              </ul>
            </div>
          )}
          {/* Customer Support */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-6">Customer Support</h4>
            <ul className="space-y-3">
              <li>
                <LocalizedClientLink
                  href="/account"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  My Account
                </LocalizedClientLink>
              </li>
              <li>
                <LocalizedClientLink
                  href="/help"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  Help Center
                </LocalizedClientLink>
              </li>
              <li>
                <LocalizedClientLink
                  href="/shipping"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  Shipping Info
                </LocalizedClientLink>
              </li>
              <li>
                <LocalizedClientLink
                  href="/returns"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  Returns & Exchanges
                </LocalizedClientLink>
              </li>
              <li>
                <LocalizedClientLink
                  href="/contact"
                  className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center gap-2 group"
                >
                  <ArrowRight className="w-3 h-3 transition-transform duration-300 group-hover:translate-x-1" />
                  Contact Us
                </LocalizedClientLink>
              </li>
            </ul>
          </div>
        </div>

        {/* Social Media & Trust Badges */}
        <div className="border-t border-gray-700 pt-8 mt-12">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
            {/* Social Media */}
            <div className="flex items-center gap-4">
              <span className="text-gray-300 font-medium">Follow Us:</span>
              <div className="flex gap-3">
                <a
                  href="https://facebook.com"
                  target="_blank"
                  rel="noreferrer"
                  className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-gray-300 hover:bg-primary hover:text-white transition-all duration-300 hover:scale-110"
                >
                  <Facebook className="w-5 h-5" />
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noreferrer"
                  className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-gray-300 hover:bg-primary hover:text-white transition-all duration-300 hover:scale-110"
                >
                  <Twitter className="w-5 h-5" />
                </a>
                <a
                  href="https://instagram.com"
                  target="_blank"
                  rel="noreferrer"
                  className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-gray-300 hover:bg-primary hover:text-white transition-all duration-300 hover:scale-110"
                >
                  <Instagram className="w-5 h-5" />
                </a>
                <a
                  href="https://youtube.com"
                  target="_blank"
                  rel="noreferrer"
                  className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-gray-300 hover:bg-primary hover:text-white transition-all duration-300 hover:scale-110"
                >
                  <Youtube className="w-5 h-5" />
                </a>
              </div>
            </div>

            {/* Trust Badges */}
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Secure Payments</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Fast Shipping</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>24/7 Support</span>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-700 pt-6 mt-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <Text className="text-gray-400 text-sm">
              © 2024 PetStore. All rights reserved. Made with ❤️ for pets and their families.
            </Text>
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <LocalizedClientLink href="/privacy" className="hover:text-primary transition-colors duration-300">
                Privacy Policy
              </LocalizedClientLink>
              <LocalizedClientLink href="/terms" className="hover:text-primary transition-colors duration-300">
                Terms of Service
              </LocalizedClientLink>
              <LocalizedClientLink href="/cookies" className="hover:text-primary transition-colors duration-300">
                Cookie Policy
              </LocalizedClientLink>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
