import { getRegion } from "@lib/data/regions"
import { NextRequest, NextResponse } from "next/server"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ countryCode: string }> }
) {
  try {
    const { countryCode } = await params
    const region = await getRegion(countryCode)
    
    if (!region) {
      return NextResponse.json(
        { error: "Region not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ region })
  } catch (error) {
    console.error("Error fetching region:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
