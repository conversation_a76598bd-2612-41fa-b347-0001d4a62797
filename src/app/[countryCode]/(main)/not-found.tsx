'use client'

import { <PERSON>ada<PERSON> } from "next"
import Link from 'next/link'
import { Home, Search, ArrowLeft, Heart, HelpCircle } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Animated 404 */}
        <div className="mb-8">
          <div className="relative">
            <h1 className="text-9xl font-bold text-primary opacity-20 select-none">
              404
            </h1>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center animate-bounce">
                <Heart className="w-16 h-16 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        <div className="space-y-6 mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-text-primary">
            Oops! Page Not Found
          </h2>
          <p className="text-lg text-text-secondary leading-relaxed">
            It looks like the page you're looking for has wandered off like a curious pet.
            Don't worry, we'll help you find your way back home!
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Link
            href="/"
            className="inline-flex items-center gap-3 btn-primary hover:scale-105 transition-transform duration-300"
          >
            <Home className="w-5 h-5" />
            Back to Home
          </Link>

          <Link
            href="/store"
            className="inline-flex items-center gap-3 btn-secondary hover:scale-105 transition-transform duration-300"
          >
            <Search className="w-5 h-5" />
            Browse Products
          </Link>
        </div>

        {/* Helpful Links */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
          <h3 className="text-xl font-semibold text-text-primary mb-6">
            Maybe you're looking for:
          </h3>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Link
              href="/store?category=feeders"
              className="flex items-center gap-3 p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors duration-300 group"
            >
              <div className="w-10 h-10 bg-primary-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                🍽️
              </div>
              <div className="text-left">
                <div className="font-medium text-text-primary">Smart Feeders</div>
                <div className="text-sm text-text-secondary">Automatic feeding solutions</div>
              </div>
            </Link>

            <Link
              href="/store?category=toys"
              className="flex items-center gap-3 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-300 group"
            >
              <div className="w-10 h-10 bg-accent-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                🎾
              </div>
              <div className="text-left">
                <div className="font-medium text-text-primary">Interactive Toys</div>
                <div className="text-sm text-text-secondary">Fun and engaging playtime</div>
              </div>
            </Link>

            <Link
              href="/account"
              className="flex items-center gap-3 p-4 bg-secondary-100 rounded-lg hover:bg-secondary-200 transition-colors duration-300 group"
            >
              <div className="w-10 h-10 bg-secondary-300 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                👤
              </div>
              <div className="text-left">
                <div className="font-medium text-text-primary">My Account</div>
                <div className="text-sm text-text-secondary">Manage your profile</div>
              </div>
            </Link>

            <Link
              href="/help"
              className="flex items-center gap-3 p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-300 group"
            >
              <div className="w-10 h-10 bg-green-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <HelpCircle className="w-5 h-5 text-green-600" />
              </div>
              <div className="text-left">
                <div className="font-medium text-text-primary">Help Center</div>
                <div className="text-sm text-text-secondary">Get support and answers</div>
              </div>
            </Link>
          </div>
        </div>

        {/* Fun Pet Fact */}
        <div className="mt-8 p-6 bg-gradient-to-r from-primary-100 to-accent-100 rounded-2xl">
          <h4 className="font-semibold text-text-primary mb-2">🐾 Fun Pet Fact</h4>
          <p className="text-text-secondary text-sm">
            Did you know? Cats spend 70% of their lives sleeping, which is 13-16 hours a day.
            That's why they need the perfect feeding schedule with our smart feeders!
          </p>
        </div>
      </div>
    </div>
  )
}
