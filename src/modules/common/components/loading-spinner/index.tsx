'use client'

import { Heart } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'accent'
  showText?: boolean
  text?: string
}

const LoadingSpinner = ({ 
  size = 'md', 
  variant = 'primary', 
  showText = false,
  text = 'Loading...'
}: LoadingSpinnerProps) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const variantClasses = {
    primary: 'text-primary',
    secondary: 'text-text-secondary',
    accent: 'text-accent'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }

  return (
    <div className="flex flex-col items-center justify-center gap-3">
      {/* Animated Heart Spinner */}
      <div className="relative">
        <div className={`${sizeClasses[size]} ${variantClasses[variant]} animate-spin`}>
          <svg className="w-full h-full" viewBox="0 0 24 24" fill="none">
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeDasharray="60"
              strokeDashoffset="60"
              className="animate-pulse"
            />
          </svg>
        </div>
        <Heart className={`absolute inset-0 m-auto ${sizeClasses[size]} ${variantClasses[variant]} animate-pulse`} />
      </div>

      {/* Loading Text */}
      {showText && (
        <p className={`${textSizeClasses[size]} ${variantClasses[variant]} font-medium animate-pulse`}>
          {text}
        </p>
      )}
    </div>
  )
}

export default LoadingSpinner
