import { Metadata } from "next"
import { Mail, Phone, MessageCircle, Clock, MapPin, HelpCircle } from "lucide-react"

export const metadata: Metadata = {
  title: "Support - PetShop",
  description: "Get help and support for your pet care needs. Contact our expert team for assistance with products, orders, and pet care advice.",
}

export default function SupportPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary to-accent text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            We're Here to Help
          </h1>
          <p className="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
            Get expert support for all your pet care needs. Our dedicated team is ready to assist you.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        {/* Contact Methods */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {/* Email Support */}
          <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-6 mx-auto">
              <Mail className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-center mb-4">Email Support</h3>
            <p className="text-gray-600 text-center mb-6">
              Send us an email and we'll respond within 24 hours
            </p>
            <div className="text-center">
              <a 
                href="mailto:<EMAIL>"
                className="text-primary font-semibold hover:text-accent transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Phone Support */}
          <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mb-6 mx-auto">
              <Phone className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-center mb-4">Phone Support</h3>
            <p className="text-gray-600 text-center mb-6">
              Call us for immediate assistance with urgent matters
            </p>
            <div className="text-center">
              <a 
                href="tel:******-PET-CARE"
                className="text-primary font-semibold hover:text-accent transition-colors"
              >
                +1 (800) PET-CARE
              </a>
            </div>
          </div>

          {/* Live Chat */}
          <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mb-6 mx-auto">
              <MessageCircle className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-center mb-4">Live Chat</h3>
            <p className="text-gray-600 text-center mb-6">
              Chat with our support team in real-time
            </p>
            <div className="text-center">
              <button className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-accent transition-colors">
                Start Chat
              </button>
            </div>
          </div>
        </div>

        {/* Support Hours */}
        <div className="bg-white rounded-2xl p-8 shadow-lg mb-16">
          <div className="flex items-center justify-center mb-6">
            <Clock className="w-8 h-8 text-primary mr-3" />
            <h2 className="text-2xl font-bold">Support Hours</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-4">Customer Support</h3>
              <div className="space-y-2 text-gray-600">
                <p><strong>Monday - Friday:</strong> 8:00 AM - 8:00 PM EST</p>
                <p><strong>Saturday:</strong> 9:00 AM - 6:00 PM EST</p>
                <p><strong>Sunday:</strong> 10:00 AM - 4:00 PM EST</p>
              </div>
            </div>
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-4">Emergency Pet Care</h3>
              <div className="space-y-2 text-gray-600">
                <p><strong>24/7 Emergency Line:</strong></p>
                <p className="text-primary font-semibold">+1 (800) PET-HELP</p>
                <p className="text-sm">For urgent pet health concerns</p>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-2xl p-8 shadow-lg mb-16">
          <div className="flex items-center justify-center mb-8">
            <HelpCircle className="w-8 h-8 text-primary mr-3" />
            <h2 className="text-2xl font-bold">Frequently Asked Questions</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold mb-2">How do I track my order?</h3>
                <p className="text-gray-600">You can track your order by logging into your account and viewing your order history, or by using the tracking number sent to your email.</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">What is your return policy?</h3>
                <p className="text-gray-600">We offer a 30-day return policy for unused items in original packaging. Pet food and health products may have different return requirements.</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Do you offer pet care advice?</h3>
                <p className="text-gray-600">Yes! Our team includes certified pet care specialists who can provide guidance on product selection and general pet care tips.</p>
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold mb-2">How fast is shipping?</h3>
                <p className="text-gray-600">Standard shipping takes 3-5 business days. Express shipping (1-2 days) and same-day delivery are available in select areas.</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Do you have a loyalty program?</h3>
                <p className="text-gray-600">Yes! Join our PetShop Rewards program to earn points on every purchase and get exclusive discounts and early access to new products.</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Can I cancel or modify my order?</h3>
                <p className="text-gray-600">Orders can be modified or cancelled within 1 hour of placement. After that, please contact our support team for assistance.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Form */}
        <div className="bg-white rounded-2xl p-8 shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-8">Send us a Message</h2>
          <form className="max-w-2xl mx-auto space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Your Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Enter your name"
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Enter your email"
                />
              </div>
            </div>
            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                Subject
              </label>
              <select
                id="subject"
                name="subject"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select a topic</option>
                <option value="order">Order Support</option>
                <option value="product">Product Question</option>
                <option value="return">Returns & Exchanges</option>
                <option value="technical">Technical Support</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                rows={6}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Tell us how we can help you..."
              ></textarea>
            </div>
            <div className="text-center">
              <button
                type="submit"
                className="bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-accent transition-colors duration-300"
              >
                Send Message
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
