const axios = require('axios');

const MEDUSA_BACKEND_URL = 'http://localhost:9000';
let JWT_TOKEN = null;

// 生成占位图片URL
function generatePlaceholderImage(width = 600, height = 600, text = 'Product', bgColor = '4A90E2', textColor = 'FFFFFF') {
  const encodedText = encodeURIComponent(text);
  return `https://via.placeholder.com/${width}x${height}/${bgColor}/${textColor}?text=${encodedText}`;
}

// 生成随机价格
function generateRandomPrice(basePrice, variance = 0.15) {
  const min = basePrice * (1 - variance);
  const max = basePrice * (1 + variance);
  const price = Math.floor(Math.random() * (max - min) + min);
  return Math.round(price / 100) * 100; // 四舍五入到最近的100
}

// 登录获取JWT token
async function login() {
  try {
    const response = await axios.post(`${MEDUSA_BACKEND_URL}/auth/user/emailpass`, {
      email: '<EMAIL>',
      password: 'supersecret'
    });
    JWT_TOKEN = response.data.token;
    console.log('✅ Login successful');
    return JWT_TOKEN;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

// 增强的产品数据，包含图片和更好的价格
const enhancedProducts = [
  {
    title: "PETSHOP Smart Pet Feeder with HD Camera",
    subtitle: "Model: F10 Pro - WiFi Enabled with Night Vision",
    description: `Advanced smart pet feeder with integrated HD camera and WiFi connectivity. Perfect for monitoring and feeding your pets remotely.

Features:
- 1080p HD Camera with Night Vision
- WiFi Remote Control (2.4G & 5G)
- 6L Food Capacity
- 1-10 Meals Per Day
- Voice Recording & Playback
- Mobile App Control
- Dual Power Supply (AC + Battery Backup)
- Food Jam Detection
- Stainless Steel Bowl`,
    handle: "petshop-smart-pet-feeder-hd-camera",
    status: "published",
    options: [{ title: "Color", values: ["White", "Black"] }],
    variants: [
      {
        title: "White",
        options: { Color: "White" },
        prices: [
          { amount: generateRandomPrice(14999), currency_code: "usd" },
          { amount: generateRandomPrice(18999), currency_code: "usd", price_list_id: "original" }
        ]
      },
      {
        title: "Black",
        options: { Color: "Black" },
        prices: [
          { amount: generateRandomPrice(14999), currency_code: "usd" },
          { amount: generateRandomPrice(18999), currency_code: "usd", price_list_id: "original" }
        ]
      }
    ],
    images: [
      { url: generatePlaceholderImage(600, 600, 'Smart Feeder Pro', '2ECC71', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'HD Camera View', '3498DB', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Night Vision', '34495E', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Mobile App', '9B59B6', 'FFFFFF') }
    ],
    weight: 3500,
    length: 28,
    width: 38,
    height: 42
  },
  {
    title: "Stainless Steel Water Fountain for Large Dogs",
    subtitle: "Model: W800 - 3.2L Capacity with Multi-Level Flow",
    description: `Premium stainless steel water fountain designed for large breed dogs. Features multi-level water flow and ultra-quiet operation.

Features:
- 3.2L Large Capacity
- 304 Stainless Steel Construction
- Multi-Level Water Flow
- Ultra-Quiet Pump (<30dB)
- LED Water Level Indicator
- Easy Disassembly for Cleaning
- Non-Slip Base
- Replaceable Carbon Filter`,
    handle: "stainless-steel-water-fountain-large-dogs",
    status: "published",
    options: [{ title: "Default", values: ["Standard"] }],
    variants: [
      {
        title: "Standard",
        options: { Default: "Standard" },
        prices: [
          { amount: generateRandomPrice(7999), currency_code: "usd" },
          { amount: generateRandomPrice(9999), currency_code: "usd", price_list_id: "original" }
        ]
      }
    ],
    images: [
      { url: generatePlaceholderImage(600, 600, 'Steel Fountain', '95A5A6', '2C3E50') },
      { url: generatePlaceholderImage(600, 600, 'Multi Level Flow', '3498DB', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Large Capacity', 'E74C3C', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Easy Clean', '2ECC71', 'FFFFFF') }
    ],
    weight: 2800,
    length: 26,
    width: 26,
    height: 18
  },
  {
    title: "Interactive Laser Toy for Cats",
    subtitle: "Model: L200 - Automatic Laser Pointer with Timer",
    description: `Automatic laser toy that keeps your cats entertained with random laser patterns. Features timer settings and safe laser technology.

Features:
- Safe Class 1 Laser
- Random Pattern Generation
- 15/30/45 Minute Timer
- 360° Rotation
- Battery Powered (4 AA)
- Silent Operation
- Auto Shut-off Safety
- Adjustable Height`,
    handle: "interactive-laser-toy-cats",
    status: "published",
    options: [{ title: "Color", values: ["Red", "Blue"] }],
    variants: [
      {
        title: "Red",
        options: { Color: "Red" },
        prices: [
          { amount: generateRandomPrice(3999), currency_code: "usd" },
          { amount: generateRandomPrice(5499), currency_code: "usd", price_list_id: "original" }
        ]
      },
      {
        title: "Blue",
        options: { Color: "Blue" },
        prices: [
          { amount: generateRandomPrice(3999), currency_code: "usd" },
          { amount: generateRandomPrice(5499), currency_code: "usd", price_list_id: "original" }
        ]
      }
    ],
    images: [
      { url: generatePlaceholderImage(600, 600, 'Laser Toy', 'E74C3C', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Auto Rotation', 'F39C12', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Timer Control', '8E44AD', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Cat Playing', 'E91E63', 'FFFFFF') }
    ],
    weight: 800,
    length: 15,
    width: 15,
    height: 20
  },
  {
    title: "Premium Pet Grooming Kit",
    subtitle: "Professional 7-in-1 Grooming Set",
    description: `Complete professional grooming kit for dogs and cats. Includes all essential tools for maintaining your pet's coat and hygiene.

Includes:
- Slicker Brush
- Pin Brush  
- Nail Clippers
- Nail File
- Ear Cleaning Tool
- Tooth Brush
- Grooming Scissors
- Storage Case`,
    handle: "premium-pet-grooming-kit",
    status: "published",
    options: [{ title: "Default", values: ["Standard"] }],
    variants: [
      {
        title: "Standard",
        options: { Default: "Standard" },
        prices: [
          { amount: generateRandomPrice(4999), currency_code: "usd" },
          { amount: generateRandomPrice(6999), currency_code: "usd", price_list_id: "original" }
        ]
      }
    ],
    images: [
      { url: generatePlaceholderImage(600, 600, 'Grooming Kit', '16A085', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, '7 in 1 Tools', 'D35400', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Professional Grade', '8E44AD', 'FFFFFF') },
      { url: generatePlaceholderImage(600, 600, 'Storage Case', '2C3E50', 'FFFFFF') }
    ],
    weight: 1200,
    length: 25,
    width: 20,
    height: 8
  }
];

async function createProduct(productData, categoryId) {
  try {
    const response = await axios.post(`${MEDUSA_BACKEND_URL}/admin/products`, {
      ...productData,
      categories: [{ id: categoryId }]
    }, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ Created product: ${productData.title}`);
    return response.data.product;
  } catch (error) {
    console.error(`❌ Error creating product ${productData.title}:`, error.response?.data || error.message);
    return null;
  }
}

async function getCategories() {
  try {
    const response = await axios.get(`${MEDUSA_BACKEND_URL}/admin/product-categories`, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`
      }
    });
    return response.data.product_categories;
  } catch (error) {
    console.error('❌ Error fetching categories:', error.response?.data || error.message);
    return [];
  }
}

async function main() {
  console.log('🚀 Starting enhanced product creation...');
  
  // 先登录
  await login();
  
  // 获取categories
  const categories = await getCategories();
  console.log('📋 Available categories:', categories.map(c => c.name));
  
  // 创建产品映射
  const categoryMap = {
    'feeder': categories.find(c => c.name.toLowerCase().includes('feeder')),
    'fountain': categories.find(c => c.name.toLowerCase().includes('fountain')),
    'toy': categories.find(c => c.name.toLowerCase() === 'toys'),
    'accessory': categories.find(c => c.name.toLowerCase() === 'accessories')
  };
  
  // 创建增强产品
  console.log('\n📦 Creating enhanced products...');
  
  // Smart Feeder -> Feeder category
  if (categoryMap['feeder']) {
    await createProduct(enhancedProducts[0], categoryMap['feeder'].id);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Water Fountain -> Fountain category  
  if (categoryMap['fountain']) {
    await createProduct(enhancedProducts[1], categoryMap['fountain'].id);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Laser Toy -> Toy category
  if (categoryMap['toy']) {
    await createProduct(enhancedProducts[2], categoryMap['toy'].id);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Grooming Kit -> Accessory category
  if (categoryMap['accessory']) {
    await createProduct(enhancedProducts[3], categoryMap['accessory'].id);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n✅ Enhanced product creation completed!');
}

// 运行脚本
main().catch(console.error);
