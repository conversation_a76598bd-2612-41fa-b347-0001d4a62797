import { Metadata } from "next"
import CompareTemplate from "@modules/compare/templates"

export const metadata: Metadata = {
  title: "Compare Products | Pet Store",
  description: "Compare pet products side by side to find the perfect match for your furry friends. Compare features, specifications, and prices.",
}

type Props = {
  params: Promise<{ countryCode: string }>
}

export default async function ComparePage({ params }: Props) {
  const { countryCode } = await params

  return <CompareTemplate countryCode={countryCode} />
}
