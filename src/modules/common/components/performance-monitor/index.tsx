'use client'

import { useEffect } from 'react'

interface PerformanceMonitorProps {
  enabled?: boolean
}

const PerformanceMonitor = ({ enabled = process.env.NODE_ENV === 'development' }: PerformanceMonitorProps) => {
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return

    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime)
        }
        if (entry.entryType === 'first-input') {
          console.log('FID:', entry.processingStart - entry.startTime)
        }
        if (entry.entryType === 'layout-shift') {
          if (!entry.hadRecentInput) {
            console.log('CLS:', entry.value)
          }
        }
      })
    })

    // Observe different performance metrics
    try {
      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] })
    } catch (e) {
      // Fallback for browsers that don't support all entry types
      console.warn('Performance monitoring not fully supported')
    }

    // Monitor page load performance
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        console.log('Page Load Time:', navigation.loadEventEnd - navigation.fetchStart)
        console.log('DOM Content Loaded:', navigation.domContentLoadedEventEnd - navigation.fetchStart)
        console.log('First Byte:', navigation.responseStart - navigation.fetchStart)
      }
    })

    return () => {
      observer.disconnect()
    }
  }, [enabled])

  return null
}

export default PerformanceMonitor
