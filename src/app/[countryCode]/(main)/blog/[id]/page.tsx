import { Metadata } from "next"
import { Calendar, User, ArrowLeft, Heart, MessageCircle, Share2, Clock } from "lucide-react"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import { notFound } from "next/navigation"

// Mock blog data - in a real app, this would come from a CMS or API
const blogPosts = [
  {
    id: 1,
    title: "10 Essential Tips for First-Time Pet Owners",
    excerpt: "Starting your journey as a pet parent? Here are the most important things you need to know to give your new furry friend the best care possible.",
    content: `
      <h2>Getting Started as a Pet Parent</h2>
      <p>Getting a pet for the first time is an exciting milestone, but it can also feel overwhelming. There's so much to learn and prepare for! Don't worry – with the right knowledge and preparation, you'll be well on your way to providing excellent care for your new companion.</p>
      
      <h3>1. Choose the Right Pet for Your Lifestyle</h3>
      <p>Before bringing a pet home, honestly assess your living situation, schedule, and energy level. Different pets have different needs:</p>
      <ul>
        <li>Dogs require daily exercise and social interaction</li>
        <li>Cats are more independent but still need attention and play</li>
        <li>Small pets like hamsters or birds have specific habitat requirements</li>
      </ul>
      
      <h3>2. Pet-Proof Your Home</h3>
      <p>Make your home safe by removing or securing potential hazards:</p>
      <ul>
        <li>Toxic plants and foods</li>
        <li>Small objects that could be swallowed</li>
        <li>Electrical cords and chemicals</li>
        <li>Secure cabinets and trash cans</li>
      </ul>
      
      <h3>3. Establish a Routine</h3>
      <p>Pets thrive on routine. Establish consistent times for feeding, exercise, and sleep. This helps reduce anxiety and makes training easier.</p>
      
      <h3>4. Find a Good Veterinarian</h3>
      <p>Research and choose a veterinarian before you need one. Schedule a wellness check within the first week of bringing your pet home.</p>
      
      <h3>5. Invest in Quality Food</h3>
      <p>Good nutrition is the foundation of health. Choose age-appropriate, high-quality food and avoid frequent brand changes.</p>
      
      <h3>6. Start Training Early</h3>
      <p>Begin basic training and socialization as soon as possible. Positive reinforcement works best for most pets.</p>
      
      <h3>7. Budget for Pet Expenses</h3>
      <p>Pet ownership involves ongoing costs including food, veterinary care, grooming, and supplies. Plan your budget accordingly.</p>
      
      <h3>8. Provide Mental Stimulation</h3>
      <p>Bored pets can become destructive. Provide toys, puzzles, and activities to keep your pet mentally engaged.</p>
      
      <h3>9. Be Patient</h3>
      <p>Adjustment takes time. Your pet may need weeks or months to fully settle into their new home.</p>
      
      <h3>10. Enjoy the Journey</h3>
      <p>Pet ownership is incredibly rewarding. Take time to bond with your new companion and enjoy the special relationship you're building.</p>
      
      <h2>Conclusion</h2>
      <p>Remember, every pet is unique, and what works for one may not work for another. Stay flexible, keep learning, and don't hesitate to ask for help when you need it. Your veterinarian, pet store staff, and experienced pet owners can all be valuable resources.</p>
    `,
    author: "Dr. Sarah Johnson",
    date: "2024-01-15",
    category: "Pet Care",
    image: "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=1200&h=600&fit=crop",
    readTime: "5 min read",
    featured: true
  },
  // Add more posts as needed...
]

interface BlogPostPageProps {
  params: {
    id: string
    countryCode: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = blogPosts.find(p => p.id === parseInt(params.id))
  
  if (!post) {
    return {
      title: "Post Not Found - PetShop Blog",
    }
  }

  return {
    title: `${post.title} - PetShop Blog`,
    description: post.excerpt,
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const post = blogPosts.find(p => p.id === parseInt(params.id))

  if (!post) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50">
      {/* Hero Section */}
      <div className="relative">
        <img
          src={post.image}
          alt={post.title}
          className="w-full h-96 object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-end">
          <div className="container mx-auto px-4 pb-8">
            <div className="max-w-4xl">
              <div className="flex items-center gap-4 mb-4">
                <span className="bg-primary text-white px-4 py-2 rounded-full text-sm font-medium">
                  {post.category}
                </span>
                <span className="text-white text-sm flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  {post.readTime}
                </span>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                {post.title}
              </h1>
              <p className="text-xl text-white opacity-90 mb-6">
                {post.excerpt}
              </p>
              <div className="flex items-center gap-6 text-white">
                <div className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  <span>{post.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  <span>{new Date(post.date).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <LocalizedClientLink
            href="/blog"
            className="inline-flex items-center gap-2 text-primary hover:text-accent transition-colors duration-300 mb-8"
          >
            <ArrowLeft className="w-5 h-5" />
            Back to Blog
          </LocalizedClientLink>

          {/* Article Content */}
          <article className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <div
              className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-h2:text-2xl prose-h2:font-bold prose-h2:mt-8 prose-h2:mb-4 prose-h3:text-xl prose-h3:font-semibold prose-h3:mt-6 prose-h3:mb-3 prose-p:text-gray-700 prose-p:leading-relaxed prose-ul:text-gray-700 prose-li:mb-2"
              dangerouslySetInnerHTML={{ __html: post.content }}
            />
          </article>

          {/* Social Actions */}
          <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-gray-600">Share this article:</span>
                <div className="flex items-center gap-3">
                  <button className="p-2 text-gray-400 hover:text-blue-500 transition-colors">
                    <Share2 className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
                    <Heart className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-green-500 transition-colors">
                    <MessageCircle className="w-5 h-5" />
                  </button>
                </div>
              </div>
              <LocalizedClientLink
                href="/blog"
                className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-accent transition-colors duration-300"
              >
                More Articles
              </LocalizedClientLink>
            </div>
          </div>

          {/* Author Bio */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h3 className="text-xl font-bold mb-4">About the Author</h3>
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-lg">{post.author}</h4>
                <p className="text-gray-600">
                  {post.author.startsWith('Dr.') 
                    ? "Veterinarian and pet care specialist with over 10 years of experience helping pet owners provide the best care for their furry friends."
                    : "Pet care enthusiast and content creator dedicated to sharing practical tips and insights for pet owners."
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
