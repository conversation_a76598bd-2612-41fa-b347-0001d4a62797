import { defineWidgetConfig } from "@medusajs/admin-sdk"
import { DetailWidgetProps, AdminProduct } from "@medusajs/types"
import { Container, Heading, Text, Button, Input, Textarea, Badge } from "@medusajs/ui"
import { useState } from "react"

// Enhanced Product Widget for PetShop-style product management
const EnhancedProductWidget = ({ data }: DetailWidgetProps<AdminProduct>) => {
  const [activeTab, setActiveTab] = useState("overview")
  const [productData, setProductData] = useState({
    features: data.metadata?.features || [],
    specifications: data.metadata?.specifications || {},
    faq: data.metadata?.faq || [],
    marketing: data.metadata?.marketing || {},
    warranty: data.metadata?.warranty || "",
    shipping: data.metadata?.shipping || ""
  })

  const tabs = [
    { id: "overview", label: "Overview" },
    { id: "features", label: "Features" },
    { id: "specs", label: "Specifications" },
    { id: "faq", label: "FAQ" },
    { id: "marketing", label: "Marketing" }
  ]

  const handleSave = async () => {
    try {
      // Update product metadata with enhanced data
      const response = await fetch(`/admin/products/${data.id}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          metadata: {
            ...data.metadata,
            ...productData
          }
        })
      })
      
      if (response.ok) {
        alert("Product enhanced data saved successfully!")
      }
    } catch (error) {
      console.error("Error saving enhanced data:", error)
      alert("Error saving data")
    }
  }

  return (
    <Container className="p-6">
      <div className="flex items-center justify-between mb-6">
        <Heading level="h2">Enhanced Product Management</Heading>
        <Button onClick={handleSave} variant="primary">
          Save Enhanced Data
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
              activeTab === tab.id
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === "overview" && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Product Highlights</label>
              <Textarea
                placeholder="Enter key product highlights..."
                value={productData.marketing.highlights || ""}
                onChange={(e) => setProductData({
                  ...productData,
                  marketing: { ...productData.marketing, highlights: e.target.value }
                })}
                rows={4}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Warranty Info</label>
                <Input
                  placeholder="e.g., 24-Month Extended Warranty"
                  value={productData.warranty}
                  onChange={(e) => setProductData({ ...productData, warranty: e.target.value })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Shipping Info</label>
                <Input
                  placeholder="e.g., Fast Shipping, 3-5 days"
                  value={productData.shipping}
                  onChange={(e) => setProductData({ ...productData, shipping: e.target.value })}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Discount Badge</label>
              <div className="flex gap-2">
                <Input
                  placeholder="Discount percentage (e.g., 23)"
                  value={productData.marketing.discount || ""}
                  onChange={(e) => setProductData({
                    ...productData,
                    marketing: { ...productData.marketing, discount: e.target.value }
                  })}
                />
                {productData.marketing.discount && (
                  <Badge variant="secondary">-{productData.marketing.discount}%</Badge>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === "features" && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Heading level="h3">Product Features</Heading>
              <Button
                variant="secondary"
                onClick={() => setProductData({
                  ...productData,
                  features: [...productData.features, { title: "", description: "", icon: "" }]
                })}
              >
                Add Feature
              </Button>
            </div>
            
            {productData.features.map((feature: any, index: number) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-center">
                  <Text className="font-medium">Feature {index + 1}</Text>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => {
                      const newFeatures = productData.features.filter((_: any, i: number) => i !== index)
                      setProductData({ ...productData, features: newFeatures })
                    }}
                  >
                    Remove
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <Input
                    placeholder="Feature title"
                    value={feature.title}
                    onChange={(e) => {
                      const newFeatures = [...productData.features]
                      newFeatures[index] = { ...feature, title: e.target.value }
                      setProductData({ ...productData, features: newFeatures })
                    }}
                  />
                  <Input
                    placeholder="Icon URL or class"
                    value={feature.icon}
                    onChange={(e) => {
                      const newFeatures = [...productData.features]
                      newFeatures[index] = { ...feature, icon: e.target.value }
                      setProductData({ ...productData, features: newFeatures })
                    }}
                  />
                </div>
                
                <Textarea
                  placeholder="Feature description"
                  value={feature.description}
                  onChange={(e) => {
                    const newFeatures = [...productData.features]
                    newFeatures[index] = { ...feature, description: e.target.value }
                    setProductData({ ...productData, features: newFeatures })
                  }}
                  rows={3}
                />
              </div>
            ))}
          </div>
        )}

        {activeTab === "specs" && (
          <div className="space-y-4">
            <Heading level="h3">Technical Specifications</Heading>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Capacity</label>
                <Input
                  placeholder="e.g., 7L"
                  value={productData.specifications.capacity || ""}
                  onChange={(e) => setProductData({
                    ...productData,
                    specifications: { ...productData.specifications, capacity: e.target.value }
                  })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">WiFi Support</label>
                <Input
                  placeholder="e.g., 2.4G & 5G"
                  value={productData.specifications.wifi || ""}
                  onChange={(e) => setProductData({
                    ...productData,
                    specifications: { ...productData.specifications, wifi: e.target.value }
                  })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Meal Range</label>
                <Input
                  placeholder="e.g., 1-6 meals per day"
                  value={productData.specifications.mealRange || ""}
                  onChange={(e) => setProductData({
                    ...productData,
                    specifications: { ...productData.specifications, mealRange: e.target.value }
                  })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Portion Range</label>
                <Input
                  placeholder="e.g., 1-20 portions per meal"
                  value={productData.specifications.portionRange || ""}
                  onChange={(e) => setProductData({
                    ...productData,
                    specifications: { ...productData.specifications, portionRange: e.target.value }
                  })}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === "faq" && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Heading level="h3">Frequently Asked Questions</Heading>
              <Button
                variant="secondary"
                onClick={() => setProductData({
                  ...productData,
                  faq: [...productData.faq, { question: "", answer: "" }]
                })}
              >
                Add FAQ
              </Button>
            </div>
            
            {productData.faq.map((item: any, index: number) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-center">
                  <Text className="font-medium">FAQ {index + 1}</Text>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => {
                      const newFaq = productData.faq.filter((_: any, i: number) => i !== index)
                      setProductData({ ...productData, faq: newFaq })
                    }}
                  >
                    Remove
                  </Button>
                </div>
                
                <Input
                  placeholder="Question"
                  value={item.question}
                  onChange={(e) => {
                    const newFaq = [...productData.faq]
                    newFaq[index] = { ...item, question: e.target.value }
                    setProductData({ ...productData, faq: newFaq })
                  }}
                />
                
                <Textarea
                  placeholder="Answer"
                  value={item.answer}
                  onChange={(e) => {
                    const newFaq = [...productData.faq]
                    newFaq[index] = { ...item, answer: e.target.value }
                    setProductData({ ...productData, faq: newFaq })
                  }}
                  rows={3}
                />
              </div>
            ))}
          </div>
        )}

        {activeTab === "marketing" && (
          <div className="space-y-4">
            <Heading level="h3">Marketing Elements</Heading>
            
            <div>
              <label className="block text-sm font-medium mb-2">Payment Methods</label>
              <Textarea
                placeholder="Enter supported payment methods (comma separated)"
                value={productData.marketing.paymentMethods || ""}
                onChange={(e) => setProductData({
                  ...productData,
                  marketing: { ...productData.marketing, paymentMethods: e.target.value }
                })}
                rows={2}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Trust Badges</label>
              <Textarea
                placeholder="Enter trust badges (e.g., 30-day Money Back, 24/7 Support)"
                value={productData.marketing.trustBadges || ""}
                onChange={(e) => setProductData({
                  ...productData,
                  marketing: { ...productData.marketing, trustBadges: e.target.value }
                })}
                rows={2}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Call to Action</label>
              <Input
                placeholder="e.g., Buy Now, Add to Cart"
                value={productData.marketing.cta || ""}
                onChange={(e) => setProductData({
                  ...productData,
                  marketing: { ...productData.marketing, cta: e.target.value }
                })}
              />
            </div>
          </div>
        )}
      </div>
    </Container>
  )
}

// Widget configuration
export default defineWidgetConfig({
  zone: "product.details.after",
})(EnhancedProductWidget)
