'use client'

import { Heart } from 'lucide-react'
import { HttpTypes } from "@medusajs/types"
import { useWishlist } from '@lib/context/wishlist-context'

interface WishlistButtonProps {
  product: HttpTypes.StoreProduct
  variant?: 'default' | 'icon-only' | 'large'
  className?: string
}

const WishlistButton = ({ product, variant = 'default', className = '' }: WishlistButtonProps) => {
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()
  const inWishlist = isInWishlist(product.id)

  const handleToggle = () => {
    if (inWishlist) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product)
    }
  }

  const baseClasses = "transition-all duration-300 ease-brand active:scale-95"
  
  const variantClasses = {
    'default': `flex items-center gap-2 px-4 py-2 rounded-lg font-medium ${
      inWishlist 
        ? 'bg-red-50 text-red-600 hover:bg-red-100' 
        : 'bg-secondary-100 text-text-primary hover:bg-red-50 hover:text-red-600'
    }`,
    'icon-only': `p-2 rounded-lg ${
      inWishlist 
        ? 'bg-red-50 text-red-600 hover:bg-red-100' 
        : 'bg-secondary-100 text-text-secondary hover:bg-red-50 hover:text-red-600'
    }`,
    'large': `flex items-center gap-3 px-6 py-3 rounded-lg font-semibold text-lg ${
      inWishlist 
        ? 'bg-red-50 text-red-600 hover:bg-red-100' 
        : 'bg-secondary-100 text-text-primary hover:bg-red-50 hover:text-red-600'
    }`
  }

  return (
    <button
      onClick={handleToggle}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      title={inWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
    >
      <Heart 
        className={`${variant === 'large' ? 'w-6 h-6' : 'w-5 h-5'} ${
          inWishlist ? 'fill-current' : ''
        }`} 
      />
      {variant !== 'icon-only' && (
        <span>
          {inWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
        </span>
      )}
    </button>
  )
}

export default WishlistButton
