'use client'

import { useState, useEffect, useRef } from 'react'
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react'

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const [isVisible, setIsVisible] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const sectionRef = useRef<HTMLDivElement>(null)

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "<PERSON> Mom",
      location: "San Francisco, CA",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "The smart feeder has been a game-changer for our family. <PERSON> gets his meals on time even when we're stuck in traffic. The camera feature gives us peace of mind, and the app is incredibly user-friendly.",
      petName: "<PERSON>",
      petBreed: "Golden Retriever"
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "<PERSON> Dad",
      location: "New York, NY",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "Luna was a picky eater, but the portion control and scheduling features helped establish a healthy routine. The build quality is exceptional, and customer service went above and beyond to help us set it up.",
      petName: "Luna",
      petBreed: "Persian Cat"
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      role: "Pet Parent",
      location: "Austin, TX",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "I travel frequently for work, and this system has been a lifesaver. The real-time notifications and video calls with Buddy make me feel connected even when I'm thousands of miles away. Highly recommended!",
      petName: "Buddy",
      petBreed: "Labrador Mix"
    },
    {
      id: 4,
      name: "David Thompson",
      role: "Senior Pet Owner",
      location: "Seattle, WA",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "At 70, I wasn't sure about 'smart' pet products, but this has made caring for Bella so much easier. The automatic feeding takes the worry out of her medication schedule, and the support team was incredibly patient.",
      petName: "Bella",
      petBreed: "Beagle"
    }
  ]

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.3 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    if (!isPaused) {
      const interval = setInterval(() => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
      }, 5000)

      return () => clearInterval(interval)
    }
  }, [isPaused, testimonials.length])

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const currentData = testimonials[currentTestimonial]

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-primary-50 to-accent-50">
      <div className="content-container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`transition-all duration-600 ease-brand ${
              isVisible ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
            }`}
          >
            <span className="inline-block bg-white text-primary-700 px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Customer Stories
            </span>
            <h2 className="section-title">
              What Pet Parents Say
            </h2>
            <p className="section-subtitle max-w-2xl mx-auto">
              Join thousands of happy pet parents who trust our products to keep their furry friends healthy and happy.
            </p>
          </div>
        </div>

        {/* Testimonial Card */}
        <div 
          className={`max-w-4xl mx-auto transition-all duration-600 ease-brand ${
            isVisible ? 'animate-scale-in opacity-100' : 'opacity-0 scale-95'
          }`}
          style={{ animationDelay: '300ms' }}
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
        >
          <div className="bg-white rounded-3xl shadow-2xl p-8 md:p-12 relative overflow-hidden">
            {/* Background Quote */}
            <Quote className="absolute top-8 right-8 w-24 h-24 text-primary-100 opacity-50" />
            
            {/* Content */}
            <div className="relative z-10">
              {/* Rating */}
              <div className="flex items-center justify-center gap-1 mb-6">
                {[...Array(currentData.rating)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 fill-accent text-accent" />
                ))}
              </div>

              {/* Testimonial Text */}
              <blockquote className="text-xl md:text-2xl text-text-primary text-center leading-relaxed mb-8 font-serif">
                "{currentData.text}"
              </blockquote>

              {/* Customer Info */}
              <div className="flex flex-col md:flex-row items-center justify-center gap-6">
                <div className="flex items-center gap-4">
                  <img
                    src={currentData.avatar}
                    alt={currentData.name}
                    className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-lg"
                  />
                  <div className="text-center md:text-left">
                    <div className="font-semibold text-text-primary text-lg">
                      {currentData.name}
                    </div>
                    <div className="text-text-secondary">
                      {currentData.role} • {currentData.location}
                    </div>
                  </div>
                </div>

                <div className="hidden md:block w-px h-12 bg-secondary-300"></div>

                <div className="text-center">
                  <div className="text-primary font-semibold">
                    Pet: {currentData.petName}
                  </div>
                  <div className="text-text-secondary text-sm">
                    {currentData.petBreed}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-center gap-4 mt-8">
          <button
            onClick={prevTestimonial}
            className="p-3 bg-white rounded-full shadow-lg transition-all duration-300 ease-brand hover:shadow-xl hover:scale-110 active:scale-95"
          >
            <ChevronLeft className="w-6 h-6 text-text-primary" />
          </button>

          {/* Dots Indicator */}
          <div className="flex gap-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ease-brand ${
                  index === currentTestimonial 
                    ? 'bg-primary scale-125' 
                    : 'bg-white hover:bg-primary-200'
                }`}
              />
            ))}
          </div>

          <button
            onClick={nextTestimonial}
            className="p-3 bg-white rounded-full shadow-lg transition-all duration-300 ease-brand hover:shadow-xl hover:scale-110 active:scale-95"
          >
            <ChevronRight className="w-6 h-6 text-text-primary" />
          </button>
        </div>

        {/* Trust Indicators */}
        <div 
          className={`mt-16 text-center transition-all duration-600 ease-brand ${
            isVisible ? 'animate-slide-up opacity-100' : 'opacity-0 translate-y-8'
          }`}
          style={{ animationDelay: '600ms' }}
        >
          <div className="flex flex-wrap items-center justify-center gap-8 text-text-secondary">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium">Verified Reviews</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium">Real Customers</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-sm font-medium">Authentic Stories</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Testimonials
