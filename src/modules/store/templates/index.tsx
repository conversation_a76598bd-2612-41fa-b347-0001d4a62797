"use client"

import { Suspense, useState } from "react"
import { Filter, Grid, List, Search, ChevronDown, X } from "lucide-react"

import SkeletonProductGrid from "@modules/skeletons/templates/skeleton-product-grid"
import PetShopFilters from "@modules/store/components/petshop-filters"
import MobileFilterModal from "@modules/store/components/mobile-filter-modal"
import { SortOptions } from "@modules/store/components/refinement-list/sort-products"
import { ProductCardSkeleton } from "@modules/common/components/skeleton"
import LocalizedClientLink from "@modules/common/components/localized-client-link"

import PaginatedProducts from "./paginated-products"

const StoreTemplate = ({
  sortBy,
  page,
  countryCode,
  category,
}: {
  sortBy?: SortOptions
  page?: string
  countryCode: string
  category?: string
}) => {
  const pageNumber = page ? parseInt(page) : 1
  const sort = sortBy || "created_at"
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [selectedSort, setSelectedSort] = useState(sort)

  const sortOptions = [
    { value: "best-selling", label: "Best selling" },
    { value: "a-z", label: "Alphabetically, A-Z" },
    { value: "z-a", label: "Alphabetically, Z-A" },
    { value: "low-to-high", label: "Price, low to high" },
    { value: "high-to-low", label: "Price, high to low" },
    { value: "old-to-new", label: "Date, old to new" },
    { value: "new-to-old", label: "Date, new to old" }
  ]

  const categories = [
    { name: "All", slug: "all", href: "/store" },
    { name: "Feeders", slug: "automatic-feeder", href: "/store?category=automatic-feeder", isNew: true },
    { name: "Fountains", slug: "water-pet-fountain", href: "/store?category=water-pet-fountain" },
    { name: "Toys", slug: "toys", href: "/store?category=toys" },
    { name: "Accessories", slug: "accessories", href: "/store?category=accessories" }
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - PetShop Style */}
      <div className="bg-white border-b border-gray-200">
        <div className="content-container py-8">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 uppercase tracking-wide">
              WHICH ARE RIGHT FOR YOU
            </h1>
            <div className="w-24 h-1 bg-orange-500 mx-auto mb-8"></div>
          </div>
        </div>
      </div>

      {/* Filter and Sort Bar */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="content-container py-4">
          <div className="flex items-center justify-between">
            {/* Mobile Filter Button */}
            <button
              onClick={() => setShowMobileFilters(true)}
              className="lg:hidden flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Filter className="w-4 h-4" />
              Filter
            </button>

            {/* Sort Dropdown */}
            <div className="relative">
              <select
                value={selectedSort}
                onChange={(e) => setSelectedSort(e.target.value)}
                className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {/* Category Tabs */}
          <div className="flex items-center gap-1 mt-4 overflow-x-auto">
            {categories.map((categoryItem) => {
              const isActive = categoryItem.slug === 'all' ? !category : categoryItem.slug === category
              return (
                <LocalizedClientLink
                  key={categoryItem.slug}
                  href={categoryItem.href}
                  className={`relative px-4 py-2 rounded-lg whitespace-nowrap transition-colors duration-200 ${
                    isActive
                      ? 'bg-orange-500 text-white'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {categoryItem.name}
                  {categoryItem.isNew && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1 rounded">
                      New
                    </span>
                  )}
                </LocalizedClientLink>
              )
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="content-container py-8">
        <div className="flex gap-8" data-testid="category-container">
          {/* Desktop Sidebar - Filters */}
          <div className="lg:w-80 flex-shrink-0 hidden lg:block">
            <div className="sticky top-32">
              <PetShopFilters />
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            <Suspense
              fallback={
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(9)].map((_, i) => (
                    <ProductCardSkeleton key={i} />
                  ))}
                </div>
              }
            >
              <PaginatedProducts
                sortBy={sort}
                page={pageNumber}
                countryCode={countryCode}
                categoryId={category}
              />
            </Suspense>
          </div>
        </div>
      </div>

      {/* Mobile Filter Modal */}
      <MobileFilterModal
        isOpen={showMobileFilters}
        onClose={() => setShowMobileFilters(false)}
      />
    </div>
  )
}

export default StoreTemplate
