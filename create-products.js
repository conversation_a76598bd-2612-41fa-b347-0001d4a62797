const axios = require('axios');

const MEDUSA_BACKEND_URL = 'http://localhost:9000';
let JWT_TOKEN = null;

// 生成占位图片URL
function generatePlaceholderImage(width = 400, height = 400, text = 'Product') {
  const encodedText = encodeURIComponent(text);
  return `https://via.placeholder.com/${width}x${height}/4A90E2/FFFFFF?text=${encodedText}`;
}

// 生成随机价格
function generateRandomPrice(basePrice, variance = 0.2) {
  const min = basePrice * (1 - variance);
  const max = basePrice * (1 + variance);
  const price = Math.floor(Math.random() * (max - min) + min);
  return Math.round(price / 100) * 100; // 四舍五入到最近的100
}

// 登录获取JWT token
async function login() {
  try {
    const response = await axios.post(`${MEDUSA_BACKEND_URL}/auth/user/emailpass`, {
      email: '<EMAIL>',
      password: 'supersecret'
    });
    JWT_TOKEN = response.data.token;
    console.log('✅ Login successful');
    return JWT_TOKEN;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

// PetShop产品数据
const petshopProducts = {
  feeders: [
    {
      title: "Pioneer Plus Smart Wifi Pet Feeder For Cats & Dogs",
      subtitle: "Model: FV01 Plus",
      description: `Smart APP Control: Remotely set times to feed your pet automatically, with Pioneer Plus Smart Wifi Pet Feeder For Cats & Dogs you can set 1-6 meals per day and up to 20 portions of dry food per meal depending on your pet's needs.

Compatible with 2.4G and 5G. HD Camera: PETSHOP dog feeder automatic with camera allows you to see, hear how your beloved pet is eating when you're on the go.

Features:
- 7L Tank Capacity
- 6-Meal Per Day
- 20-Portion Max. Per Meal
- 10g Per Portion
- 1080p HD Camera with Night Vision
- 120° Wide-angle Lens
- WiFi Compatible (2.4G and 5G)
- Dual Power Mode (AC + Battery Backup)`,
      handle: "pioneer-plus-smart-wifi-pet-feeder-v2",
      status: "published",
      options: [{ title: "Color", values: ["Grey", "White"] }],
      variants: [
        {
          title: "Grey",
          options: { Color: "Grey" },
          prices: [
            { amount: generateRandomPrice(12999), currency_code: "usd" },
            { amount: generateRandomPrice(15999), currency_code: "usd", price_list_id: "original" }
          ]
        },
        {
          title: "White",
          options: { Color: "White" },
          prices: [
            { amount: generateRandomPrice(12999), currency_code: "usd" },
            { amount: generateRandomPrice(15999), currency_code: "usd", price_list_id: "original" }
          ]
        }
      ],
      images: [
        generatePlaceholderImage(600, 600, 'Smart Pet Feeder'),
        generatePlaceholderImage(600, 600, 'Feeder Side View'),
        generatePlaceholderImage(600, 600, 'Feeder App Control'),
        generatePlaceholderImage(600, 600, 'Feeder Features')
      ],
      weight: 3000,
      length: 25,
      width: 35,
      height: 39
    },
    {
      title: "Heritage View Pet Feeder Dual Bowls For Two Cats",
      subtitle: "Model: HV10DP",
      description: `Heritage View Pet Feeder with dual bowls designed specifically for two cats. Features advanced feeding technology and monitoring capabilities.

Perfect for multi-pet households with precise portion control and scheduling features.`,
      handle: "heritage-view-pet-feeder-dual-bowls",
      status: "published",
      options: [{ title: "Default", values: ["Standard"] }],
      variants: [
        {
          title: "Standard",
          options: { Default: "Standard" },
          prices: [
            { amount: generateRandomPrice(7999), currency_code: "usd" },
            { amount: generateRandomPrice(9999), currency_code: "usd", price_list_id: "original" }
          ]
        }
      ],
      images: [
        generatePlaceholderImage(600, 600, 'Dual Bowl Feeder'),
        generatePlaceholderImage(600, 600, 'Heritage Design'),
        generatePlaceholderImage(600, 600, 'Two Cat Setup')
      ],
      weight: 2500,
      length: 30,
      width: 25,
      height: 35
    }
  ],
  fountains: [
    {
      title: "Ceramic Wireless Pet Water Fountain",
      subtitle: "Model: W03",
      description: `New ceramic wireless pet water fountain with advanced filtration system. Perfect for cats and dogs who prefer fresh, flowing water.

Features wireless operation and easy maintenance design.`,
      handle: "ceramic-wireless-pet-water-fountain",
      status: "published",
      options: [{ title: "Default", values: ["Standard"] }],
      variants: [
        {
          title: "Standard",
          options: { Default: "Standard" },
          prices: [
            { amount: generateRandomPrice(6999), currency_code: "usd" },
            { amount: generateRandomPrice(8999), currency_code: "usd", price_list_id: "original" }
          ]
        }
      ],
      images: [
        generatePlaceholderImage(600, 600, 'Ceramic Fountain'),
        generatePlaceholderImage(600, 600, 'Wireless Design'),
        generatePlaceholderImage(600, 600, 'Water Flow')
      ],
      weight: 2000,
      length: 20,
      width: 20,
      height: 25
    },
    {
      title: "Basin Pet Water Fountain for Large Dogs",
      subtitle: "Model: W600",
      description: `Basin Pet Water Fountain designed specifically for large dogs. Features large capacity and durable construction.

Perfect for big breeds with high water consumption needs.`,
      handle: "basin-pet-water-fountain-w600",
      status: "published",
      options: [{ title: "Default", values: ["Standard"] }],
      variants: [
        {
          title: "Standard",
          options: { Default: "Standard" },
          prices: [
            { amount: generateRandomPrice(4999), currency_code: "usd" },
            { amount: generateRandomPrice(6999), currency_code: "usd", price_list_id: "original" }
          ]
        }
      ],
      images: [
        generatePlaceholderImage(600, 600, 'Large Dog Fountain'),
        generatePlaceholderImage(600, 600, 'Basin Design'),
        generatePlaceholderImage(600, 600, 'Big Capacity')
      ],
      weight: 2500,
      length: 25,
      width: 25,
      height: 20
    },
    {
      title: "Brook Stainless Steel Cat Fountain",
      subtitle: "Model: F10 - Provide Enough Water for Your Pet While You are Away",
      description: `Brook Stainless Steel Cat Fountain with premium stainless steel construction. Provides fresh, filtered water for your pets.

Durable and easy to clean with modern design.`,
      handle: "brook-stainless-steel-cat-fountain",
      status: "published",
      options: [{ title: "Default", values: ["Standard"] }],
      variants: [
        {
          title: "Standard",
          options: { Default: "Standard" },
          prices: [
            { amount: generateRandomPrice(3999), currency_code: "usd" },
            { amount: generateRandomPrice(5499), currency_code: "usd", price_list_id: "original" }
          ]
        }
      ],
      images: [
        generatePlaceholderImage(600, 600, 'Steel Cat Fountain'),
        generatePlaceholderImage(600, 600, 'Stainless Design'),
        generatePlaceholderImage(600, 600, 'Premium Quality')
      ],
      weight: 1800,
      length: 18,
      width: 18,
      height: 22
    }
  ],
  toys: [
    {
      title: "Interactive Smart Pet Toy",
      subtitle: "Model: T001",
      description: `Interactive smart pet toy with motion sensors and automatic play modes. Keeps your pets entertained when you're away.

Features multiple play patterns and durable construction.`,
      handle: "interactive-smart-pet-toy",
      status: "published",
      options: [{ title: "Color", values: ["Blue", "Red"] }],
      variants: [
        {
          title: "Blue",
          options: { Color: "Blue" },
          prices: [
            { amount: generateRandomPrice(2999), currency_code: "usd" },
            { amount: generateRandomPrice(3999), currency_code: "usd", price_list_id: "original" }
          ]
        },
        {
          title: "Red",
          options: { Color: "Red" },
          prices: [
            { amount: generateRandomPrice(2999), currency_code: "usd" },
            { amount: generateRandomPrice(3999), currency_code: "usd", price_list_id: "original" }
          ]
        }
      ],
      images: [
        generatePlaceholderImage(600, 600, 'Smart Pet Toy'),
        generatePlaceholderImage(600, 600, 'Interactive Play'),
        generatePlaceholderImage(600, 600, 'Motion Sensor')
      ],
      weight: 500,
      length: 15,
      width: 15,
      height: 10
    }
  ],
  accessories: [
    {
      title: "Pet Camera Monitoring System",
      subtitle: "Model: A001",
      description: `Advanced pet camera monitoring system with two-way audio and night vision. Keep an eye on your pets from anywhere.

Features HD video quality and mobile app integration.`,
      handle: "pet-camera-monitoring-system",
      status: "published",
      options: [{ title: "Default", values: ["Standard"] }],
      variants: [
        {
          title: "Standard",
          options: { Default: "Standard" },
          prices: [
            { amount: generateRandomPrice(5999), currency_code: "usd" },
            { amount: generateRandomPrice(7999), currency_code: "usd", price_list_id: "original" }
          ]
        }
      ],
      images: [
        generatePlaceholderImage(600, 600, 'Pet Camera'),
        generatePlaceholderImage(600, 600, 'Monitoring System'),
        generatePlaceholderImage(600, 600, 'Night Vision'),
        generatePlaceholderImage(600, 600, 'Mobile App')
      ],
      weight: 800,
      length: 12,
      width: 8,
      height: 15
    }
  ]
};

async function createProduct(productData, categoryId) {
  try {
    const response = await axios.post(`${MEDUSA_BACKEND_URL}/admin/products`, {
      ...productData,
      categories: [{ id: categoryId }]
    }, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`✅ Created product: ${productData.title}`);
    return response.data.product;
  } catch (error) {
    console.error(`❌ Error creating product ${productData.title}:`, error.response?.data || error.message);
    return null;
  }
}

async function getCategories() {
  try {
    const response = await axios.get(`${MEDUSA_BACKEND_URL}/admin/product-categories`, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`
      }
    });
    return response.data.product_categories;
  } catch (error) {
    console.error('❌ Error fetching categories:', error.response?.data || error.message);
    return [];
  }
}

async function main() {
  console.log('🚀 Starting product creation...');

  // 先登录
  await login();

  // 获取categories
  const categories = await getCategories();
  console.log('📋 Available categories:', categories.map(c => c.name));

  // 创建产品映射
  const categoryMap = {
    'automatic feeder': categories.find(c => c.name.toLowerCase().includes('feeder')),
    'water pet fountain': categories.find(c => c.name.toLowerCase().includes('fountain')),
    'toys': categories.find(c => c.name.toLowerCase() === 'toys'),
    'accessories': categories.find(c => c.name.toLowerCase() === 'accessories')
  };

  // 创建Feeders类目的产品
  if (categoryMap['automatic feeder']) {
    console.log('\\n📦 Creating Feeder products...');
    for (const product of petshopProducts.feeders) {
      await createProduct(product, categoryMap['automatic feeder'].id);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }
  }

  // 创建Fountains类目的产品
  if (categoryMap['water pet fountain']) {
    console.log('\\n💧 Creating Fountain products...');
    for (const product of petshopProducts.fountains) {
      await createProduct(product, categoryMap['water pet fountain'].id);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }
  }

  // 创建Toys类目的产品
  if (categoryMap['toys']) {
    console.log('\\n🎾 Creating Toy products...');
    for (const product of petshopProducts.toys) {
      await createProduct(product, categoryMap['toys'].id);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }
  }

  // 创建Accessories类目的产品
  if (categoryMap['accessories']) {
    console.log('\\n🔧 Creating Accessory products...');
    for (const product of petshopProducts.accessories) {
      await createProduct(product, categoryMap['accessories'].id);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }
  }

  console.log('\\n✅ Product creation completed!');
}

if (require.main === module) {
  main();
}

module.exports = { createProduct, getCategories, petshopProducts };
