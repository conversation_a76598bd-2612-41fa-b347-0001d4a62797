import { Metadata } from "next"
import { notFound } from "next/navigation"
import { listProducts } from "@lib/data/products"
import { getRegion } from "@lib/data/regions"
import SearchResults from "@modules/search/templates"

type Props = {
  params: Promise<{ countryCode: string }>
  searchParams: Promise<{ q?: string; page?: string }>
}

export async function generateMetadata({ searchParams }: Props): Promise<Metadata> {
  const params = await searchParams
  const query = params.q || ''

  return {
    title: query ? `Search results for "${query}" | Pet Store` : 'Search | Pet Store',
    description: query 
      ? `Find the best pet products for "${query}". Browse our curated selection of premium pet care items.`
      : 'Search our extensive collection of premium pet products and accessories.',
  }
}

export default async function SearchPage({ params, searchParams }: Props) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const { countryCode } = resolvedParams
  const { q: query, page } = resolvedSearchParams

  const region = await getRegion(countryCode)
  
  if (!region) {
    notFound()
  }

  const pageNumber = page ? parseInt(page) : 1
  const searchQuery = query || ''

  // Get search results
  const { response: productsResponse } = await listProducts({
    countryCode,
    queryParams: { 
      q: searchQuery,
      limit: 20,
      offset: (pageNumber - 1) * 20,
      fields: "*variants.calculated_price,+variants.inventory_quantity,+metadata,+tags,+categories"
    },
  })

  const products = productsResponse.products
  const totalCount = productsResponse.count || 0

  return (
    <SearchResults
      products={products}
      query={searchQuery}
      totalCount={totalCount}
      currentPage={pageNumber}
      region={region}
      countryCode={countryCode}
    />
  )
}
