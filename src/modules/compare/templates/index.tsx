'use client'

import { Scale, X, ArrowLeft, ShoppingCart, Heart } from 'lucide-react'
import Link from 'next/link'
import { useCompare } from '@lib/context/compare-context'
import LocalizedClientLink from '@modules/common/components/localized-client-link'

interface CompareTemplateProps {
  countryCode: string
}

const CompareTemplate = ({ countryCode }: CompareTemplateProps) => {
  const { state, removeFromCompare, clearCompare } = useCompare()
  const { items, isLoading } = state

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price / 100)
  }

  const getFeatureValue = (item: any, feature: string) => {
    const metadata = item.metadata || {}
    const features = metadata.features || {}
    return features[feature] || metadata[feature] || 'N/A'
  }

  const getSpecValue = (item: any, spec: string) => {
    const metadata = item.metadata || {}
    const specifications = metadata.specifications || {}
    return specifications[spec] || 'N/A'
  }

  const comparisonFeatures = [
    { key: 'capacity', label: 'Capacity' },
    { key: 'connectivity', label: 'Connectivity' },
    { key: 'power_source', label: 'Power Source' },
    { key: 'material', label: 'Material' },
    { key: 'warranty', label: 'Warranty' },
    { key: 'dimensions', label: 'Dimensions' },
    { key: 'weight', label: 'Weight' }
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-secondary-50 to-white">
        <div className="content-container py-12">
          <div className="animate-pulse">
            <div className="h-8 bg-secondary-200 rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white rounded-2xl p-6 shadow-lg">
                  <div className="aspect-square bg-secondary-200 rounded-lg mb-4"></div>
                  <div className="space-y-3">
                    <div className="h-4 bg-secondary-200 rounded"></div>
                    <div className="h-4 bg-secondary-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-secondary-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-secondary-200">
        <div className="content-container py-8">
          <div className="flex items-center gap-4 mb-6">
            <LocalizedClientLink
              href="/store"
              className="flex items-center gap-2 text-text-secondary hover:text-primary transition-colors duration-300"
            >
              <ArrowLeft className="w-5 h-5" />
              Continue Shopping
            </LocalizedClientLink>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <Scale className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-text-primary">Product Comparison</h1>
                <p className="text-text-secondary">
                  {items.length > 0 ? `Comparing ${items.length} product${items.length !== 1 ? 's' : ''}` : 'No products to compare'}
                </p>
              </div>
            </div>

            {items.length > 0 && (
              <button
                onClick={clearCompare}
                className="flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-700 border border-red-300 rounded-lg hover:bg-red-50 transition-colors duration-300"
              >
                <X className="w-4 h-4" />
                Clear All
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="content-container py-12">
        {items.length > 0 ? (
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            {/* Product Headers */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-6 border-b border-secondary-200">
              {items.map((item) => (
                <div key={item.id} className="text-center relative">
                  <button
                    onClick={() => removeFromCompare(item.id)}
                    className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors duration-300 z-10"
                  >
                    <X className="w-4 h-4" />
                  </button>
                  
                  <div className="aspect-square overflow-hidden rounded-lg mb-4">
                    {item.thumbnail ? (
                      <img
                        src={item.thumbnail}
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-500 text-sm">No Image</span>
                      </div>
                    )}
                  </div>
                  
                  <h3 className="font-semibold text-text-primary mb-2 line-clamp-2">
                    {item.title}
                  </h3>
                  
                  {item.price && (
                    <p className="text-xl font-bold text-primary mb-4">
                      {formatPrice(item.price)}
                    </p>
                  )}

                  <div className="flex gap-2">
                    <LocalizedClientLink
                      href={`/${countryCode}/products/${item.handle}`}
                      className="flex-1 bg-primary text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-primary-600 transition-colors duration-300"
                    >
                      View
                    </LocalizedClientLink>
                    <button
                      className="w-10 h-8 bg-secondary-100 text-text-primary rounded-lg hover:bg-accent hover:text-white transition-colors duration-300"
                      title="Add to Cart"
                    >
                      <ShoppingCart className="w-4 h-4 mx-auto" />
                    </button>
                  </div>
                </div>
              ))}
              
              {/* Empty slots */}
              {[...Array(4 - items.length)].map((_, index) => (
                <div key={`empty-${index}`} className="text-center">
                  <div className="aspect-square border-2 border-dashed border-secondary-300 rounded-lg flex items-center justify-center mb-4">
                    <div className="text-center text-text-secondary">
                      <Scale className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Add Product</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Comparison Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <tbody>
                  {/* Price Row */}
                  <tr className="border-b border-secondary-200">
                    <td className="p-4 font-semibold text-text-primary bg-secondary-50 w-48">
                      Price
                    </td>
                    {items.map((item) => (
                      <td key={item.id} className="p-4 text-center">
                        {item.price ? (
                          <span className="text-xl font-bold text-primary">
                            {formatPrice(item.price)}
                          </span>
                        ) : (
                          <span className="text-text-secondary">N/A</span>
                        )}
                      </td>
                    ))}
                    {[...Array(4 - items.length)].map((_, index) => (
                      <td key={`empty-price-${index}`} className="p-4 text-center text-text-secondary">
                        -
                      </td>
                    ))}
                  </tr>

                  {/* Feature Rows */}
                  {comparisonFeatures.map((feature) => (
                    <tr key={feature.key} className="border-b border-secondary-200">
                      <td className="p-4 font-semibold text-text-primary bg-secondary-50">
                        {feature.label}
                      </td>
                      {items.map((item) => (
                        <td key={item.id} className="p-4 text-center">
                          <span className="text-text-primary">
                            {getSpecValue(item, feature.key)}
                          </span>
                        </td>
                      ))}
                      {[...Array(4 - items.length)].map((_, index) => (
                        <td key={`empty-${feature.key}-${index}`} className="p-4 text-center text-text-secondary">
                          -
                        </td>
                      ))}
                    </tr>
                  ))}

                  {/* Category Row */}
                  <tr className="border-b border-secondary-200">
                    <td className="p-4 font-semibold text-text-primary bg-secondary-50">
                      Category
                    </td>
                    {items.map((item) => (
                      <td key={item.id} className="p-4 text-center">
                        <span className="text-text-primary">
                          {item.categories?.[0]?.name || 'N/A'}
                        </span>
                      </td>
                    ))}
                    {[...Array(4 - items.length)].map((_, index) => (
                      <td key={`empty-category-${index}`} className="p-4 text-center text-text-secondary">
                        -
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          /* Empty Compare */
          <div className="max-w-2xl mx-auto text-center bg-white rounded-2xl shadow-lg p-12">
            <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Scale className="w-16 h-16 text-blue-500" />
            </div>
            
            <h2 className="text-3xl font-bold text-text-primary mb-4">
              No products to compare
            </h2>
            
            <p className="text-lg text-text-secondary mb-8">
              Add products to your comparison list to see how they stack up against each other.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <LocalizedClientLink
                href="/store"
                className="btn-primary"
              >
                Browse Products
              </LocalizedClientLink>
              
              <LocalizedClientLink
                href="/store?category=feeders"
                className="btn-secondary"
              >
                Smart Feeders
              </LocalizedClientLink>
            </div>

            <div className="text-left bg-blue-50 rounded-lg p-6">
              <h3 className="font-semibold text-text-primary mb-4">How to compare products:</h3>
              <ul className="space-y-2 text-text-secondary text-sm">
                <li>• Browse products and click "Add to Compare"</li>
                <li>• Add up to 4 products for comparison</li>
                <li>• Compare features, specifications, and prices</li>
                <li>• Make informed decisions for your pets</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CompareTemplate
