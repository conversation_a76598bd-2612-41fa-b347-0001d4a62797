import { Suspense } from "react"
import { Heart, ShoppingBag } from "lucide-react"

import LocalizedClientLink from "@modules/common/components/localized-client-link"
import CartButton from "@modules/layout/components/cart-button"
import NavClient from "@modules/layout/components/nav-client"

export default function Nav() {

  return (
    <div className="sticky top-0 inset-x-0 z-50 group">
      {/* Top Banner */}
      <div className="bg-primary text-white text-center py-2 text-sm font-medium">
        <div className="content-container">
          🎉 Free shipping on orders over $50 | 24/7 Customer Support
        </div>
      </div>

      {/* Main Navigation */}
      <header className="relative bg-white/95 backdrop-blur-md border-b border-secondary-200 shadow-sm transition-all duration-300 ease-brand group-hover:shadow-lg">
        <nav className="content-container flex items-center justify-between h-20">
          {/* Left Section - Logo */}
          <div className="flex items-center gap-6">
            {/* Logo */}
            <LocalizedClientLink
              href="/"
              className="flex items-center gap-3 group/logo"
              data-testid="nav-store-link"
            >
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center transition-transform duration-300 ease-brand group-hover/logo:scale-110">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <div className="hidden sm:block">
                <div className="text-xl font-bold text-text-primary group-hover/logo:text-primary transition-colors duration-300">
                  PetStore
                </div>
                <div className="text-xs text-text-secondary -mt-1">
                  Premium Pet Care
                </div>
              </div>
            </LocalizedClientLink>
          </div>

          {/* Center Section - Navigation Links */}
          <div className="hidden md:flex items-center gap-8">
            <LocalizedClientLink
              href="/"
              className="relative text-text-primary hover:text-primary font-medium transition-colors duration-300 ease-brand group/link"
            >
              Home
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 ease-brand group-hover/link:w-full"></span>
            </LocalizedClientLink>

            <LocalizedClientLink
              href="/store"
              className="relative text-text-primary hover:text-primary font-medium transition-colors duration-300 ease-brand group/link"
            >
              Shop
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 ease-brand group-hover/link:w-full"></span>
            </LocalizedClientLink>

            <LocalizedClientLink
              href="/support"
              className="relative text-text-primary hover:text-primary font-medium transition-colors duration-300 ease-brand group/link"
            >
              Support
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 ease-brand group-hover/link:w-full"></span>
            </LocalizedClientLink>

            <LocalizedClientLink
              href="/blog"
              className="relative text-text-primary hover:text-primary font-medium transition-colors duration-300 ease-brand group/link"
            >
              Blog
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 ease-brand group-hover/link:w-full"></span>
            </LocalizedClientLink>
          </div>

          {/* Mobile Navigation Links */}
          <div className="flex md:hidden items-center gap-2 text-sm">
            <LocalizedClientLink
              href="/"
              className="text-text-primary hover:text-primary font-medium transition-colors duration-300 px-2 py-1"
            >
              Home
            </LocalizedClientLink>
            <LocalizedClientLink
              href="/store"
              className="text-text-primary hover:text-primary font-medium transition-colors duration-300 px-2 py-1"
            >
              Shop
            </LocalizedClientLink>
            <LocalizedClientLink
              href="/support"
              className="text-text-primary hover:text-primary font-medium transition-colors duration-300 px-2 py-1"
            >
              Support
            </LocalizedClientLink>
            <LocalizedClientLink
              href="/blog"
              className="text-text-primary hover:text-primary font-medium transition-colors duration-300 px-2 py-1"
            >
              Blog
            </LocalizedClientLink>
          </div>

          {/* Right Section - Actions */}
          <div className="flex items-center gap-4">
            <NavClient />

            {/* Cart */}
            <Suspense
              fallback={
                <LocalizedClientLink
                  className="flex items-center gap-2 p-2 text-text-secondary hover:text-primary hover:bg-primary-50 rounded-lg transition-all duration-300 ease-brand hover:scale-110 active:scale-95"
                  href="/cart"
                  data-testid="nav-cart-link"
                >
                  <ShoppingBag className="w-5 h-5" />
                  <span className="hidden md:block text-sm font-medium">Cart (0)</span>
                </LocalizedClientLink>
              }
            >
              <CartButton />
            </Suspense>
          </div>
        </nav>
      </header>
    </div>
  )
}
