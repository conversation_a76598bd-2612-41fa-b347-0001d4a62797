const path = require("path")

module.exports = {
  darkMode: "class",
  presets: [require("@medusajs/ui-preset")],
  content: [
    "./src/app/**/*.{js,ts,jsx,tsx}",
    "./src/pages/**/*.{js,ts,jsx,tsx}",
    "./src/components/**/*.{js,ts,jsx,tsx}",
    "./src/modules/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@medusajs/ui/dist/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      transitionProperty: {
        width: "width margin",
        height: "height",
        bg: "background-color",
        display: "display opacity",
        visibility: "visibility",
        padding: "padding-top padding-right padding-bottom padding-left",
        transform: "transform",
        all: "all",
      },
      transitionTimingFunction: {
        'brand': 'cubic-bezier(0.4, 0, 0.2, 1)',
        'smooth': 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'bounce-in': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      },
      transitionDuration: {
        '250': '250ms',
        '300': '300ms',
        '400': '400ms',
        '600': '600ms',
        '800': '800ms',
        '1200': '1200ms',
      },
      colors: {
        // Brand Colors
        primary: {
          DEFAULT: "#5A5AFF",
          50: "#F0F0FF",
          100: "#E6E6FF",
          200: "#CCCCFF",
          300: "#B3B3FF",
          400: "#8080FF",
          500: "#5A5AFF",
          600: "#4747E6",
          700: "#3333CC",
          800: "#2626B3",
          900: "#1A1A99",
        },
        secondary: {
          DEFAULT: "#F2F2F2",
          50: "#FAFAFA",
          100: "#F2F2F2",
          200: "#E8E8E8",
          300: "#DEDEDE",
          400: "#CACACA",
          500: "#B6B6B6",
          600: "#A2A2A2",
          700: "#8E8E8E",
          800: "#7A7A7A",
          900: "#666666",
        },
        accent: {
          DEFAULT: "#FFC107",
          50: "#FFF8E1",
          100: "#FFECB3",
          200: "#FFE082",
          300: "#FFD54F",
          400: "#FFCA28",
          500: "#FFC107",
          600: "#FFB300",
          700: "#FFA000",
          800: "#FF8F00",
          900: "#FF6F00",
        },
        text: {
          primary: "#1A1A1A",
          secondary: "#666666",
        },
        // Keep existing grey colors for compatibility
        grey: {
          0: "#FFFFFF",
          5: "#F9FAFB",
          10: "#F3F4F6",
          20: "#E5E7EB",
          30: "#D1D5DB",
          40: "#9CA3AF",
          50: "#6B7280",
          60: "#4B5563",
          70: "#374151",
          80: "#1F2937",
          90: "#111827",
        },
      },
      borderRadius: {
        none: "0px",
        soft: "2px",
        base: "4px",
        rounded: "8px",
        large: "16px",
        circle: "9999px",
      },
      maxWidth: {
        "8xl": "100rem",
      },
      screens: {
        "2xsmall": "320px",
        xsmall: "512px",
        small: "1024px",
        medium: "1280px",
        large: "1440px",
        xlarge: "1680px",
        "2xlarge": "1920px",
      },
      fontSize: {
        "3xl": "2rem",
      },
      fontFamily: {
        sans: [
          "Inter",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neue",
          "Ubuntu",
          "sans-serif",
        ],
        serif: [
          "Source Serif Pro",
          "Georgia",
          "Cambria",
          "Times New Roman",
          "Times",
          "serif",
        ],
      },
      spacing: {
        // 8px grid system
        '0.5': '2px',   // 0.5 * 4px
        '1': '4px',     // 1 * 4px
        '1.5': '6px',   // 1.5 * 4px
        '2': '8px',     // 2 * 4px (base unit)
        '2.5': '10px',  // 2.5 * 4px
        '3': '12px',    // 3 * 4px
        '3.5': '14px',  // 3.5 * 4px
        '4': '16px',    // 4 * 4px
        '5': '20px',    // 5 * 4px
        '6': '24px',    // 6 * 4px
        '7': '28px',    // 7 * 4px
        '8': '32px',    // 8 * 4px (2 * base)
        '9': '36px',    // 9 * 4px
        '10': '40px',   // 10 * 4px
        '11': '44px',   // 11 * 4px
        '12': '48px',   // 12 * 4px (3 * base)
        '14': '56px',   // 14 * 4px
        '16': '64px',   // 16 * 4px (4 * base)
        '20': '80px',   // 20 * 4px (5 * base)
        '24': '96px',   // 24 * 4px (6 * base)
        '28': '112px',  // 28 * 4px (7 * base)
        '32': '128px',  // 32 * 4px (8 * base)
        '36': '144px',  // 36 * 4px (9 * base)
        '40': '160px',  // 40 * 4px (10 * base)
        '44': '176px',  // 44 * 4px (11 * base)
        '48': '192px',  // 48 * 4px (12 * base)
        '52': '208px',  // 52 * 4px (13 * base)
        '56': '224px',  // 56 * 4px (14 * base)
        '60': '240px',  // 60 * 4px (15 * base)
        '64': '256px',  // 64 * 4px (16 * base)
        '72': '288px',  // 72 * 4px (18 * base)
        '80': '320px',  // 80 * 4px (20 * base)
        '96': '384px',  // 96 * 4px (24 * base)
      },
      keyframes: {
        // Brand animations
        'hero-blur-to-clear': {
          '0%': {
            filter: 'blur(4px)',
            transform: 'scale(1.05)',
            opacity: '0.8'
          },
          '100%': {
            filter: 'blur(0px)',
            transform: 'scale(1)',
            opacity: '1'
          },
        },
        'slide-in-left': {
          '0%': {
            opacity: '0',
            transform: 'translateX(-50px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
        'slide-in-right': {
          '0%': {
            opacity: '0',
            transform: 'translateX(50px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
        'slide-up': {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        'float-up': {
          '0%': {
            transform: 'translateY(0px)',
          },
          '100%': {
            transform: 'translateY(-8px)',
          },
        },
        'scale-in': {
          '0%': {
            opacity: '0',
            transform: 'scale(0.9)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        'parallax': {
          '0%': { transform: 'translateY(0px)' },
          '100%': { transform: 'translateY(-20px)' },
        },
        'shimmer': {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        'slide-in-right': {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        'fade-in-up': {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        'scale-in': {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        'slide-up': {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        ring: {
          "0%": { transform: "rotate(0deg)" },
          "100%": { transform: "rotate(360deg)" },
        },
        "fade-in-right": {
          "0%": {
            opacity: "0",
            transform: "translateX(10px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateX(0)",
          },
        },
        "fade-in-top": {
          "0%": {
            opacity: "0",
            transform: "translateY(-10px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "fade-out-top": {
          "0%": {
            height: "100%",
          },
          "99%": {
            height: "0",
          },
          "100%": {
            visibility: "hidden",
          },
        },
        "accordion-slide-up": {
          "0%": {
            height: "var(--radix-accordion-content-height)",
            opacity: "1",
          },
          "100%": {
            height: "0",
            opacity: "0",
          },
        },
        "accordion-slide-down": {
          "0%": {
            "min-height": "0",
            "max-height": "0",
            opacity: "0",
          },
          "100%": {
            "min-height": "var(--radix-accordion-content-height)",
            "max-height": "none",
            opacity: "1",
          },
        },
        enter: {
          "0%": { transform: "scale(0.9)", opacity: 0 },
          "100%": { transform: "scale(1)", opacity: 1 },
        },
        leave: {
          "0%": { transform: "scale(1)", opacity: 1 },
          "100%": { transform: "scale(0.9)", opacity: 0 },
        },
        "slide-in": {
          "0%": { transform: "translateY(-100%)" },
          "100%": { transform: "translateY(0)" },
        },
      },
      animation: {
        // Brand animations
        'hero-blur-to-clear': 'hero-blur-to-clear 1200ms cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'slide-in-left': 'slide-in-left 600ms cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'slide-in-right': 'slide-in-right 600ms cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'slide-up': 'slide-up 400ms cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'float-up': 'float-up 300ms cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'scale-in': 'scale-in 300ms cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'parallax': 'parallax 1s ease-out infinite alternate',
        // Existing animations
        ring: "ring 2.2s cubic-bezier(0.5, 0, 0.5, 1) infinite",
        "fade-in-right":
          "fade-in-right 0.3s cubic-bezier(0.5, 0, 0.5, 1) forwards",
        "fade-in-top": "fade-in-top 0.2s cubic-bezier(0.5, 0, 0.5, 1) forwards",
        "fade-out-top":
          "fade-out-top 0.2s cubic-bezier(0.5, 0, 0.5, 1) forwards",
        "accordion-open":
          "accordion-slide-down 300ms cubic-bezier(0.87, 0, 0.13, 1) forwards",
        "accordion-close":
          "accordion-slide-up 300ms cubic-bezier(0.87, 0, 0.13, 1) forwards",
        enter: "enter 200ms ease-out",
        "slide-in": "slide-in 1.2s cubic-bezier(.41,.73,.51,1.02)",
        leave: "leave 150ms ease-in forwards",
      },
    },
  },
  plugins: [require("tailwindcss-radix")()],
}
