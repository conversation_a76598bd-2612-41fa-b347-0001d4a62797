'use client'

interface SkeletonProps {
  className?: string
  variant?: 'text' | 'circular' | 'rectangular'
  width?: string | number
  height?: string | number
  animation?: 'pulse' | 'wave' | 'none'
}

const Skeleton = ({ 
  className = '', 
  variant = 'rectangular',
  width,
  height,
  animation = 'pulse'
}: SkeletonProps) => {
  const baseClasses = 'bg-gradient-to-r from-secondary-200 via-secondary-300 to-secondary-200 bg-[length:200%_100%]'
  
  const variantClasses = {
    text: 'rounded-md h-4',
    circular: 'rounded-full',
    rectangular: 'rounded-lg'
  }

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-[shimmer_2s_infinite]',
    none: ''
  }

  const style = {
    width: width || undefined,
    height: height || undefined
  }

  return (
    <div 
      className={`${baseClasses} ${variantClasses[variant]} ${animationClasses[animation]} ${className}`}
      style={style}
    />
  )
}

// Product Card Skeleton
export const ProductCardSkeleton = () => (
  <div className="bg-white rounded-2xl shadow-lg overflow-hidden p-6 space-y-4">
    <Skeleton variant="rectangular" className="aspect-square w-full" />
    <div className="space-y-3">
      <Skeleton variant="text" className="w-3/4 h-5" />
      <Skeleton variant="text" className="w-full h-4" />
      <Skeleton variant="text" className="w-1/2 h-4" />
      <div className="flex justify-between items-center">
        <Skeleton variant="text" className="w-1/3 h-6" />
        <Skeleton variant="text" className="w-1/4 h-4" />
      </div>
      <Skeleton variant="rectangular" className="w-full h-10" />
    </div>
  </div>
)

// Hero Section Skeleton
export const HeroSkeleton = () => (
  <div className="h-screen bg-gradient-to-br from-secondary-100 to-secondary-200 flex items-center">
    <div className="content-container">
      <div className="max-w-4xl space-y-6">
        <Skeleton variant="rectangular" className="w-32 h-8" />
        <Skeleton variant="text" className="w-full h-12" />
        <Skeleton variant="text" className="w-3/4 h-12" />
        <Skeleton variant="text" className="w-2/3 h-6" />
        <Skeleton variant="text" className="w-1/2 h-6" />
        <div className="flex gap-4 pt-4">
          <Skeleton variant="rectangular" className="w-32 h-12" />
          <Skeleton variant="rectangular" className="w-32 h-12" />
        </div>
      </div>
    </div>
  </div>
)

// Navigation Skeleton
export const NavSkeleton = () => (
  <div className="sticky top-0 z-50 bg-white border-b border-secondary-200">
    <div className="content-container flex items-center justify-between h-20">
      <div className="flex items-center gap-6">
        <Skeleton variant="circular" className="w-10 h-10" />
        <Skeleton variant="text" className="w-24 h-6" />
      </div>
      <div className="hidden lg:flex items-center gap-8">
        <Skeleton variant="text" className="w-16 h-4" />
        <Skeleton variant="text" className="w-16 h-4" />
        <Skeleton variant="text" className="w-16 h-4" />
        <Skeleton variant="text" className="w-20 h-4" />
      </div>
      <div className="flex items-center gap-4">
        <Skeleton variant="circular" className="w-8 h-8" />
        <Skeleton variant="circular" className="w-8 h-8" />
        <Skeleton variant="circular" className="w-8 h-8" />
        <Skeleton variant="rectangular" className="w-20 h-8" />
      </div>
    </div>
  </div>
)

// Page Loading Skeleton
export const PageLoadingSkeleton = () => (
  <div className="min-h-screen bg-secondary-50">
    <NavSkeleton />
    <HeroSkeleton />
    <div className="py-20">
      <div className="content-container">
        <div className="text-center mb-16 space-y-4">
          <Skeleton variant="rectangular" className="w-32 h-6 mx-auto" />
          <Skeleton variant="text" className="w-64 h-8 mx-auto" />
          <Skeleton variant="text" className="w-96 h-4 mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <ProductCardSkeleton key={i} />
          ))}
        </div>
      </div>
    </div>
  </div>
)

export default Skeleton
