'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Search, Filter, Grid, List, ArrowLeft, SlidersHorizontal } from 'lucide-react'
import { HttpTypes } from "@medusajs/types"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import ProductPreview from "@modules/products/components/product-preview"

interface SearchResultsProps {
  products: HttpTypes.StoreProduct[]
  query: string
  totalCount: number
  currentPage: number
  region: HttpTypes.StoreRegion
  countryCode: string
}

const SearchResults = ({ 
  products, 
  query, 
  totalCount, 
  currentPage, 
  region, 
  countryCode 
}: SearchResultsProps) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('relevance')
  const [showFilters, setShowFilters] = useState(false)

  const totalPages = Math.ceil(totalCount / 20)
  const hasResults = products.length > 0

  const sortOptions = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'price_asc', label: 'Price: Low to High' },
    { value: 'price_desc', label: 'Price: High to Low' },
    { value: 'name_asc', label: 'Name: A to Z' },
    { value: 'name_desc', label: 'Name: Z to A' },
    { value: 'created_at', label: 'Newest First' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-secondary-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-secondary-200">
        <div className="content-container py-8">
          <div className="flex items-center gap-4 mb-6">
            <LocalizedClientLink
              href="/store"
              className="flex items-center gap-2 text-text-secondary hover:text-primary transition-colors duration-300"
            >
              <ArrowLeft className="w-5 h-5" />
              Back to Store
            </LocalizedClientLink>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
              <Search className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-text-primary">
                {query ? `Search Results` : 'Search Products'}
              </h1>
              <p className="text-text-secondary">
                {query ? (
                  hasResults ? (
                    `${totalCount} result${totalCount !== 1 ? 's' : ''} for "${query}"`
                  ) : (
                    `No results found for "${query}"`
                  )
                ) : (
                  'Find the perfect products for your pets'
                )}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="content-container py-12">
        {hasResults ? (
          <>
            {/* Controls */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8 bg-white rounded-2xl shadow-lg p-6">
              <div className="flex items-center gap-4">
                <span className="text-text-secondary font-medium">
                  Showing {((currentPage - 1) * 20) + 1}-{Math.min(currentPage * 20, totalCount)} of {totalCount}
                </span>
              </div>
              
              <div className="flex items-center gap-4">
                {/* Sort */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                {/* View Toggle */}
                <div className="flex items-center gap-2 bg-secondary-100 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-md transition-colors duration-300 ${
                      viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-secondary-200'
                    }`}
                  >
                    <Grid className={`w-4 h-4 ${viewMode === 'grid' ? 'text-primary' : 'text-text-secondary'}`} />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-md transition-colors duration-300 ${
                      viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-secondary-200'
                    }`}
                  >
                    <List className={`w-4 h-4 ${viewMode === 'list' ? 'text-primary' : 'text-text-secondary'}`} />
                  </button>
                </div>

                {/* Filters Toggle */}
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 px-4 py-2 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors duration-300"
                >
                  <SlidersHorizontal className="w-4 h-4" />
                  Filters
                </button>
              </div>
            </div>

            {/* Products Grid */}
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                : 'grid-cols-1'
            }`}>
              {products.map((product) => (
                <div key={product.id} className={viewMode === 'list' ? 'max-w-none' : ''}>
                  <ProductPreview
                    product={product}
                    region={region}
                    isFeatured={false}
                  />
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-12">
                <div className="flex items-center gap-2">
                  {currentPage > 1 && (
                    <Link
                      href={`/${countryCode}/search?q=${encodeURIComponent(query)}&page=${currentPage - 1}`}
                      className="px-4 py-2 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors duration-300"
                    >
                      Previous
                    </Link>
                  )}
                  
                  {[...Array(Math.min(5, totalPages))].map((_, i) => {
                    const pageNum = Math.max(1, currentPage - 2) + i
                    if (pageNum > totalPages) return null
                    
                    return (
                      <Link
                        key={pageNum}
                        href={`/${countryCode}/search?q=${encodeURIComponent(query)}&page=${pageNum}`}
                        className={`px-4 py-2 rounded-lg transition-colors duration-300 ${
                          pageNum === currentPage
                            ? 'bg-primary text-white'
                            : 'border border-secondary-300 hover:bg-secondary-50'
                        }`}
                      >
                        {pageNum}
                      </Link>
                    )
                  })}
                  
                  {currentPage < totalPages && (
                    <Link
                      href={`/${countryCode}/search?q=${encodeURIComponent(query)}&page=${currentPage + 1}`}
                      className="px-4 py-2 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors duration-300"
                    >
                      Next
                    </Link>
                  )}
                </div>
              </div>
            )}
          </>
        ) : query ? (
          /* No Results */
          <div className="max-w-2xl mx-auto text-center bg-white rounded-2xl shadow-lg p-12">
            <div className="text-6xl mb-6">🔍</div>
            <h2 className="text-2xl font-bold text-text-primary mb-4">
              No results found for "{query}"
            </h2>
            <p className="text-text-secondary mb-8">
              We couldn't find any products matching your search. Try adjusting your search terms or browse our categories.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <LocalizedClientLink
                href="/store"
                className="btn-primary"
              >
                Browse All Products
              </LocalizedClientLink>
              <LocalizedClientLink
                href="/store?category=feeders"
                className="btn-secondary"
              >
                Popular Categories
              </LocalizedClientLink>
            </div>

            <div className="text-left">
              <h3 className="font-semibold text-text-primary mb-4">Search Tips:</h3>
              <ul className="space-y-2 text-text-secondary text-sm">
                <li>• Check your spelling</li>
                <li>• Try more general terms</li>
                <li>• Use fewer keywords</li>
                <li>• Browse by category instead</li>
              </ul>
            </div>
          </div>
        ) : (
          /* Search Landing */
          <div className="max-w-2xl mx-auto text-center bg-white rounded-2xl shadow-lg p-12">
            <div className="text-6xl mb-6">🔍</div>
            <h2 className="text-2xl font-bold text-text-primary mb-4">
              Search Our Products
            </h2>
            <p className="text-text-secondary mb-8">
              Find the perfect products for your furry friends from our extensive collection.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <LocalizedClientLink
                href="/store?category=feeders"
                className="p-6 bg-primary-50 rounded-xl hover:bg-primary-100 transition-colors duration-300 group"
              >
                <div className="text-3xl mb-3">🍽️</div>
                <h3 className="font-semibold text-text-primary group-hover:text-primary transition-colors duration-300">
                  Smart Feeders
                </h3>
              </LocalizedClientLink>

              <LocalizedClientLink
                href="/store?category=toys"
                className="p-6 bg-accent-50 rounded-xl hover:bg-accent-100 transition-colors duration-300 group"
              >
                <div className="text-3xl mb-3">🎾</div>
                <h3 className="font-semibold text-text-primary group-hover:text-accent-700 transition-colors duration-300">
                  Interactive Toys
                </h3>
              </LocalizedClientLink>

              <LocalizedClientLink
                href="/store?category=accessories"
                className="p-6 bg-green-50 rounded-xl hover:bg-green-100 transition-colors duration-300 group"
              >
                <div className="text-3xl mb-3">🎀</div>
                <h3 className="font-semibold text-text-primary group-hover:text-green-700 transition-colors duration-300">
                  Accessories
                </h3>
              </LocalizedClientLink>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default SearchResults
