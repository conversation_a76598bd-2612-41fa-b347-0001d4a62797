import { Metadata } from "next"
import { Calendar, User, ArrowRight, Heart, MessageCircle, Share2 } from "lucide-react"
import LocalizedClientLink from "@modules/common/components/localized-client-link"

export const metadata: Metadata = {
  title: "Blog - PetShop",
  description: "Expert pet care tips, product reviews, and the latest news in pet health and wellness. Your trusted source for pet care information.",
}

// Mock blog data - in a real app, this would come from a CMS or API
const blogPosts = [
  {
    id: 1,
    title: "10 Essential Tips for First-Time Pet Owners",
    excerpt: "Starting your journey as a pet parent? Here are the most important things you need to know to give your new furry friend the best care possible.",
    content: "Getting a pet for the first time is an exciting milestone, but it can also feel overwhelming...",
    author: "Dr. <PERSON>",
    date: "2024-01-15",
    category: "Pet Care",
    image: "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=800&h=400&fit=crop",
    readTime: "5 min read",
    featured: true
  },
  {
    id: 2,
    title: "The Ultimate Guide to Pet Nutrition",
    excerpt: "Learn about the essential nutrients your pet needs and how to choose the right food for their age, size, and health requirements.",
    content: "Proper nutrition is the foundation of your pet's health and wellbeing...",
    author: "Dr. Michael Chen",
    date: "2024-01-12",
    category: "Nutrition",
    image: "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=800&h=400&fit=crop",
    readTime: "8 min read",
    featured: false
  },
  {
    id: 3,
    title: "Smart Pet Feeders: Technology Meets Pet Care",
    excerpt: "Discover how automated feeding systems can help maintain your pet's health and give you peace of mind when you're away.",
    content: "Modern pet care has been revolutionized by smart technology...",
    author: "Tech Team",
    date: "2024-01-10",
    category: "Technology",
    image: "https://images.unsplash.com/photo-**********-03cce0bbc87b?w=800&h=400&fit=crop",
    readTime: "6 min read",
    featured: false
  },
  {
    id: 4,
    title: "Seasonal Pet Care: Winter Safety Tips",
    excerpt: "Keep your pets safe and comfortable during the colder months with these essential winter care guidelines.",
    content: "Winter brings unique challenges for pet owners...",
    author: "Dr. Emily Rodriguez",
    date: "2024-01-08",
    category: "Seasonal Care",
    image: "https://images.unsplash.com/photo-**********-49959800b1f6?w=800&h=400&fit=crop",
    readTime: "4 min read",
    featured: false
  },
  {
    id: 5,
    title: "Understanding Pet Behavior: Signs of Stress",
    excerpt: "Learn to recognize the signs of stress in your pets and discover effective ways to help them feel more comfortable and secure.",
    content: "Pets can experience stress just like humans do...",
    author: "Dr. Lisa Thompson",
    date: "2024-01-05",
    category: "Behavior",
    image: "https://images.unsplash.com/photo-1583512603805-3cc6b41f3edb?w=800&h=400&fit=crop",
    readTime: "7 min read",
    featured: false
  },
  {
    id: 6,
    title: "DIY Pet Toys: Fun and Budget-Friendly Ideas",
    excerpt: "Create engaging and safe toys for your pets using common household items. Save money while keeping your pets entertained.",
    content: "You don't need to spend a fortune on pet toys...",
    author: "Creative Team",
    date: "2024-01-03",
    category: "DIY",
    image: "https://images.unsplash.com/photo-1601758125946-6ec2ef64daf8?w=800&h=400&fit=crop",
    readTime: "5 min read",
    featured: false
  }
]

const categories = ["All", "Pet Care", "Nutrition", "Technology", "Seasonal Care", "Behavior", "DIY"]

export default function BlogPage() {
  const featuredPost = blogPosts.find(post => post.featured)
  const regularPosts = blogPosts.filter(post => !post.featured)

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary to-accent text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Pet Care Blog
          </h1>
          <p className="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
            Expert advice, tips, and insights to help you give your pets the best care possible
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        {/* Featured Post */}
        {featuredPost && (
          <div className="mb-16">
            <h2 className="text-3xl font-bold mb-8 text-center">Featured Article</h2>
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <img
                    src={featuredPost.image}
                    alt={featuredPost.title}
                    className="w-full h-64 md:h-full object-cover"
                  />
                </div>
                <div className="md:w-1/2 p-8">
                  <div className="flex items-center gap-4 mb-4">
                    <span className="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                      {featuredPost.category}
                    </span>
                    <span className="text-gray-500 text-sm">{featuredPost.readTime}</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{featuredPost.title}</h3>
                  <p className="text-gray-600 mb-6">{featuredPost.excerpt}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <User className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-600">{featuredPost.author}</span>
                      <Calendar className="w-5 h-5 text-gray-400 ml-3" />
                      <span className="text-sm text-gray-600">
                        {new Date(featuredPost.date).toLocaleDateString()}
                      </span>
                    </div>
                    <LocalizedClientLink
                      href={`/blog/${featuredPost.id}`}
                      className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-accent transition-colors duration-300 flex items-center gap-2"
                    >
                      Read More <ArrowRight className="w-4 h-4" />
                    </LocalizedClientLink>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Category Filter */}
        <div className="mb-12">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                className="px-6 py-2 rounded-full border border-primary text-primary hover:bg-primary hover:text-white transition-colors duration-300"
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {regularPosts.map((post) => (
            <article key={post.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <div className="flex items-center gap-4 mb-3">
                  <span className="bg-accent text-white px-3 py-1 rounded-full text-xs font-medium">
                    {post.category}
                  </span>
                  <span className="text-gray-500 text-xs">{post.readTime}</span>
                </div>
                <h3 className="text-xl font-bold mb-3 line-clamp-2">{post.title}</h3>
                <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{post.author}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(post.date).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <LocalizedClientLink
                    href={`/blog/${post.id}`}
                    className="text-primary font-semibold hover:text-accent transition-colors duration-300 flex items-center gap-2"
                  >
                    Read More <ArrowRight className="w-4 h-4" />
                  </LocalizedClientLink>
                  <div className="flex items-center gap-3">
                    <button className="text-gray-400 hover:text-red-500 transition-colors">
                      <Heart className="w-5 h-5" />
                    </button>
                    <button className="text-gray-400 hover:text-blue-500 transition-colors">
                      <Share2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-lg text-center">
          <h2 className="text-2xl font-bold mb-4">Stay Updated</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Subscribe to our newsletter to get the latest pet care tips, product updates, and exclusive offers delivered to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
            <button className="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-accent transition-colors duration-300">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
