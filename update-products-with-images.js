const axios = require('axios');

const MEDUSA_BACKEND_URL = 'http://localhost:9000';
let JWT_TOKEN = null;

// 生成占位图片URL
function generatePlaceholderImage(width = 600, height = 600, text = 'Product', bgColor = '4A90E2', textColor = 'FFFFFF') {
  const encodedText = encodeURIComponent(text);
  return `https://via.placeholder.com/${width}x${height}/${bgColor}/${textColor}?text=${encodedText}`;
}

// 生成随机价格
function generateRandomPrice(basePrice, variance = 0.15) {
  const min = basePrice * (1 - variance);
  const max = basePrice * (1 + variance);
  const price = Math.floor(Math.random() * (max - min) + min);
  return Math.round(price / 100) * 100; // 四舍五入到最近的100
}

// 登录获取JWT token
async function login() {
  try {
    const response = await axios.post(`${MEDUSA_BACKEND_URL}/auth/user/emailpass`, {
      email: '<EMAIL>',
      password: 'supersecret'
    });
    JWT_TOKEN = response.data.token;
    console.log('✅ Login successful');
    return JWT_TOKEN;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

// 获取所有产品
async function getAllProducts() {
  try {
    const response = await axios.get(`${MEDUSA_BACKEND_URL}/admin/products`, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`
      }
    });
    return response.data.products;
  } catch (error) {
    console.error('❌ Error fetching products:', error.response?.data || error.message);
    return [];
  }
}

// 为产品添加图片
async function addImagesToProduct(productId, imageUrls) {
  try {
    for (const imageUrl of imageUrls) {
      await axios.post(`${MEDUSA_BACKEND_URL}/admin/products/${productId}/images`, {
        url: imageUrl
      }, {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
    }
    console.log(`✅ Added ${imageUrls.length} images to product ${productId}`);
  } catch (error) {
    console.error(`❌ Error adding images to product ${productId}:`, error.response?.data || error.message);
  }
}

// 更新产品变体价格
async function updateVariantPrices(variantId, prices) {
  try {
    await axios.post(`${MEDUSA_BACKEND_URL}/admin/products/variants/${variantId}/prices`, {
      prices: prices
    }, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    console.log(`✅ Updated prices for variant ${variantId}`);
  } catch (error) {
    console.error(`❌ Error updating prices for variant ${variantId}:`, error.response?.data || error.message);
  }
}

// 产品图片映射
const productImageMap = {
  'pioneer-plus-smart-wifi-pet-feeder': [
    generatePlaceholderImage(600, 600, 'Smart Pet Feeder', '2ECC71', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'WiFi Control', '3498DB', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'HD Camera', 'E74C3C', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'App Interface', '9B59B6', 'FFFFFF')
  ],
  'pioneer-plus-smart-wifi-pet-feeder-v2': [
    generatePlaceholderImage(600, 600, 'Heritage Feeder', 'F39C12', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Dual Bowls', '1ABC9C', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Two Cats', 'E67E22', 'FFFFFF')
  ],
  'ceramic-wireless-pet-water-fountain': [
    generatePlaceholderImage(600, 600, 'Ceramic Fountain', '8E44AD', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Wireless Design', '2980B9', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Fresh Water', '16A085', 'FFFFFF')
  ],
  'basin-pet-water-fountain-w600': [
    generatePlaceholderImage(600, 600, 'Large Dog Fountain', 'D35400', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Basin Design', '27AE60', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'High Capacity', 'F1C40F', '2C3E50')
  ],
  'brook-stainless-steel-cat-fountain': [
    generatePlaceholderImage(600, 600, 'Steel Fountain', '34495E', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Premium Quality', 'BDC3C7', '2C3E50'),
    generatePlaceholderImage(600, 600, 'Easy Clean', '95A5A6', 'FFFFFF')
  ],
  'interactive-smart-pet-toy': [
    generatePlaceholderImage(600, 600, 'Smart Toy', 'E91E63', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Interactive Play', 'FF5722', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Motion Sensor', '607D8B', 'FFFFFF')
  ],
  'pet-camera-monitoring-system': [
    generatePlaceholderImage(600, 600, 'Pet Camera', '795548', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Night Vision', '424242', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Mobile App', '009688', 'FFFFFF'),
    generatePlaceholderImage(600, 600, 'Two Way Audio', 'FF9800', 'FFFFFF')
  ]
};

// 价格映射（基础价格）
const productPriceMap = {
  'pioneer-plus-smart-wifi-pet-feeder': 12999,
  'pioneer-plus-smart-wifi-pet-feeder-v2': 7999,
  'ceramic-wireless-pet-water-fountain': 6999,
  'basin-pet-water-fountain-w600': 4999,
  'brook-stainless-steel-cat-fountain': 3999,
  'interactive-smart-pet-toy': 2999,
  'pet-camera-monitoring-system': 5999
};

async function main() {
  console.log('🚀 Starting product update with images and prices...');
  
  // 先登录
  await login();
  
  // 获取所有产品
  const products = await getAllProducts();
  console.log(`📦 Found ${products.length} products`);
  
  for (const product of products) {
    console.log(`\n🔄 Processing product: ${product.title}`);
    
    // 添加图片（如果产品没有图片或图片很少）
    if (product.images.length < 2 && productImageMap[product.handle]) {
      console.log(`📸 Adding images for ${product.handle}...`);
      await addImagesToProduct(product.id, productImageMap[product.handle]);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }
    
    // 更新价格（如果有价格映射）
    if (productPriceMap[product.handle] && product.variants) {
      console.log(`💰 Updating prices for ${product.handle}...`);
      const basePrice = productPriceMap[product.handle];
      
      for (const variant of product.variants) {
        const newPrices = [
          {
            amount: generateRandomPrice(basePrice),
            currency_code: 'usd'
          },
          {
            amount: generateRandomPrice(basePrice * 1.3),
            currency_code: 'usd',
            price_list_id: 'original'
          }
        ];
        
        await updateVariantPrices(variant.id, newPrices);
        await new Promise(resolve => setTimeout(resolve, 500)); // 等待0.5秒
      }
    }
  }
  
  console.log('\n✅ Product update completed!');
}

// 运行脚本
main().catch(console.error);
