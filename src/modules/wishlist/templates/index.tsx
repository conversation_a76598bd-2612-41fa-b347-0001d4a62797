'use client'

import { Heart, <PERSON>Cart, Trash2, <PERSON><PERSON><PERSON><PERSON>, Share2 } from 'lucide-react'
import Link from 'next/link'
import { useWishlist } from '@lib/context/wishlist-context'
import LocalizedClientLink from '@modules/common/components/localized-client-link'

interface WishlistTemplateProps {
  countryCode: string
}

const WishlistTemplate = ({ countryCode }: WishlistTemplateProps) => {
  const { state, removeFromWishlist, clearWishlist } = useWishlist()
  const { items, isLoading } = state

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price / 100)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-secondary-50 to-white">
        <div className="content-container py-12">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-secondary-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-2xl p-6 shadow-lg">
                  <div className="aspect-square bg-secondary-200 rounded-lg mb-4"></div>
                  <div className="h-4 bg-secondary-200 rounded mb-2"></div>
                  <div className="h-4 bg-secondary-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-secondary-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-secondary-200">
        <div className="content-container py-8">
          <div className="flex items-center gap-4 mb-6">
            <LocalizedClientLink
              href="/store"
              className="flex items-center gap-2 text-text-secondary hover:text-primary transition-colors duration-300"
            >
              <ArrowLeft className="w-5 h-5" />
              Continue Shopping
            </LocalizedClientLink>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-text-primary">My Wishlist</h1>
                <p className="text-text-secondary">
                  {items.length > 0 ? `${items.length} item${items.length !== 1 ? 's' : ''} saved` : 'No items saved yet'}
                </p>
              </div>
            </div>

            {items.length > 0 && (
              <div className="flex items-center gap-4">
                <button className="flex items-center gap-2 px-4 py-2 text-text-secondary hover:text-primary border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors duration-300">
                  <Share2 className="w-4 h-4" />
                  Share
                </button>
                <button
                  onClick={clearWishlist}
                  className="flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-700 border border-red-300 rounded-lg hover:bg-red-50 transition-colors duration-300"
                >
                  <Trash2 className="w-4 h-4" />
                  Clear All
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="content-container py-12">
        {items.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {items.map((item) => (
              <div key={item.id} className="bg-white rounded-2xl shadow-lg overflow-hidden group hover:shadow-xl transition-shadow duration-300">
                {/* Product Image */}
                <div className="relative aspect-square overflow-hidden">
                  {item.thumbnail ? (
                    <img
                      src={item.thumbnail}
                      alt={item.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-500 text-sm">No Image</span>
                    </div>
                  )}
                  
                  {/* Remove Button */}
                  <button
                    onClick={() => removeFromWishlist(item.id)}
                    className="absolute top-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-red-500 hover:bg-red-50 hover:text-red-600 transition-colors duration-300 opacity-0 group-hover:opacity-100"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <h3 className="font-semibold text-lg text-text-primary mb-2 line-clamp-2 group-hover:text-primary transition-colors duration-300">
                    {item.title}
                  </h3>
                  
                  {item.price && (
                    <p className="text-xl font-bold text-primary mb-4">
                      {formatPrice(item.price)}
                    </p>
                  )}

                  <div className="text-xs text-text-secondary mb-4">
                    Added {new Date(item.addedAt).toLocaleDateString()}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <LocalizedClientLink
                      href={`/${countryCode}/products/${item.handle}`}
                      className="flex-1 bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-600 transition-colors duration-300 text-center"
                    >
                      View Product
                    </LocalizedClientLink>
                    
                    <button className="flex items-center justify-center w-12 h-10 bg-secondary-100 text-text-primary rounded-lg hover:bg-accent hover:text-white transition-colors duration-300">
                      <ShoppingCart className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Empty Wishlist */
          <div className="max-w-2xl mx-auto text-center bg-white rounded-2xl shadow-lg p-12">
            <div className="w-32 h-32 bg-gradient-to-br from-red-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart className="w-16 h-16 text-red-400" />
            </div>
            
            <h2 className="text-3xl font-bold text-text-primary mb-4">
              Your wishlist is empty
            </h2>
            
            <p className="text-lg text-text-secondary mb-8">
              Start adding products you love to keep track of them and purchase later.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <LocalizedClientLink
                href="/store"
                className="btn-primary"
              >
                Explore Products
              </LocalizedClientLink>
              
              <LocalizedClientLink
                href="/store?category=feeders"
                className="btn-secondary"
              >
                Popular Items
              </LocalizedClientLink>
            </div>

            {/* Featured Categories */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <LocalizedClientLink
                href="/store?category=feeders"
                className="group p-6 bg-primary-50 rounded-xl hover:bg-primary-100 transition-colors duration-300"
              >
                <div className="text-3xl mb-3">🍽️</div>
                <h3 className="font-semibold text-text-primary group-hover:text-primary transition-colors duration-300">
                  Smart Feeders
                </h3>
              </LocalizedClientLink>

              <LocalizedClientLink
                href="/store?category=toys"
                className="group p-6 bg-accent-50 rounded-xl hover:bg-accent-100 transition-colors duration-300"
              >
                <div className="text-3xl mb-3">🎾</div>
                <h3 className="font-semibold text-text-primary group-hover:text-accent-700 transition-colors duration-300">
                  Interactive Toys
                </h3>
              </LocalizedClientLink>

              <LocalizedClientLink
                href="/store?category=accessories"
                className="group p-6 bg-green-50 rounded-xl hover:bg-green-100 transition-colors duration-300"
              >
                <div className="text-3xl mb-3">🎀</div>
                <h3 className="font-semibold text-text-primary group-hover:text-green-700 transition-colors duration-300">
                  Accessories
                </h3>
              </LocalizedClientLink>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default WishlistTemplate
