import ItemsTemplate from "./items"
import Summary from "./summary"
import EmptyCartMessage from "../components/empty-cart-message"
import SignInPrompt from "../components/sign-in-prompt"
import Divider from "@modules/common/components/divider"
import { HttpTypes } from "@medusajs/types"
import { ShoppingBag, ArrowLeft, Shield, Truck, CreditCard } from "lucide-react"
import LocalizedClientLink from "@modules/common/components/localized-client-link"

const CartTemplate = ({
  cart,
  customer,
}: {
  cart: HttpTypes.StoreCart | null
  customer: HttpTypes.StoreCustomer | null
}) => {
  const itemCount = cart?.items?.reduce((acc, item) => acc + item.quantity, 0) || 0

  return (
    <div className="min-h-screen bg-gradient-to-b from-secondary-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-secondary-200">
        <div className="content-container py-8">
          <div className="flex items-center gap-4 mb-4">
            <LocalizedClientLink
              href="/store"
              className="flex items-center gap-2 text-text-secondary hover:text-primary transition-colors duration-300"
            >
              <ArrowLeft className="w-5 h-5" />
              Continue Shopping
            </LocalizedClientLink>
          </div>

          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
              <ShoppingBag className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-text-primary">Shopping Cart</h1>
              <p className="text-text-secondary">
                {itemCount > 0 ? `${itemCount} item${itemCount !== 1 ? 's' : ''} in your cart` : 'Your cart is empty'}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="content-container py-12" data-testid="cart-container">
        {cart?.items?.length ? (
          <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-8">
            {/* Cart Items */}
            <div className="space-y-6">
              {!customer && (
                <div className="bg-white rounded-2xl shadow-lg p-6">
                  <SignInPrompt />
                </div>
              )}

              <div className="bg-white rounded-2xl shadow-lg p-6">
                <ItemsTemplate cart={cart} />
              </div>

              {/* Trust Badges */}
              <div className="bg-white rounded-2xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Why Shop With Us?</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
                    <Shield className="w-8 h-8 text-green-600" />
                    <div>
                      <div className="font-medium text-text-primary">Secure Checkout</div>
                      <div className="text-sm text-text-secondary">SSL encrypted payments</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
                    <Truck className="w-8 h-8 text-blue-600" />
                    <div>
                      <div className="font-medium text-text-primary">Fast Shipping</div>
                      <div className="text-sm text-text-secondary">Free shipping over $50</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-purple-50 rounded-lg">
                    <CreditCard className="w-8 h-8 text-purple-600" />
                    <div>
                      <div className="font-medium text-text-primary">Easy Returns</div>
                      <div className="text-sm text-text-secondary">30-day return policy</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:sticky lg:top-24 h-fit">
              {cart && cart.region && (
                <div className="bg-white rounded-2xl shadow-lg p-6">
                  <Summary cart={cart as any} />
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="max-w-2xl mx-auto">
            <EmptyCartMessage />
          </div>
        )}
      </div>
    </div>
  )
}

export default CartTemplate
