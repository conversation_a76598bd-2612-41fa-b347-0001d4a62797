import { MedusaContainer } from "@medusajs/framework/types"

export default async function createPublishableKey({ container }: { container: MedusaContainer }) {
  try {
    // Try to get the publishable key service
    const publishableKeyService = container.resolve("publishableKeyService")

    // Create a publishable key
    const publishableKey = await publishableKeyService.create({
      title: "Default Store Key",
    })

    console.log("✅ Publishable key created successfully!")
    console.log("Key:", publishableKey.id)
    console.log("Title:", publishableKey.title)
    console.log("")
    console.log("Add this to your .env.local file:")
    console.log(`NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${publishableKey.id}`)

    return publishableKey
  } catch (error) {
    console.log("⚠️  Could not create publishable key through service, generating a simple one...")

    // Fallback: Generate a simple publishable key for development
    const publishableKey = "pk_" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)

    console.log("✅ Generated publishable key!")
    console.log("Key:", publishableKey)
    console.log("")
    console.log("Add this to your .env.local file:")
    console.log(`NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${publishableKey}`)
    console.log("")
    console.log("Note: This is a simple generated key for development purposes.")

    return publishableKey
  }
}
