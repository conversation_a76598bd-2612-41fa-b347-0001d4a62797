import { SVGProps } from "react"

const TransferImage = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="280"
    height="181"
    viewBox="0 0 280 181"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="0.00428286"
      y="-0.742904"
      width="33.5"
      height="65.5"
      rx="6.75"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 189.756 88.938)"
      fill="#D4D4D8"
      stroke="#52525B"
      strokeWidth="1.5"
    />
    <rect
      x="0.00428286"
      y="-0.742904"
      width="33.5"
      height="65.5"
      rx="6.75"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 189.756 85.938)"
      fill="white"
      stroke="#52525B"
      strokeWidth="1.5"
    />
    <path
      d="M180.579 107.642L179.126 108.459"
      stroke="#52525B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      opacity="0.88"
      d="M182.305 110.046L180.257 110.034"
      stroke="#52525B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      opacity="0.75"
      d="M180.551 112.429L179.108 111.596"
      stroke="#52525B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      opacity="0.63"
      d="M176.347 113.397L176.354 112.23"
      stroke="#52525B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      opacity="0.5"
      d="M172.154 112.381L173.606 111.564"
      stroke="#52525B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      opacity="0.38"
      d="M170.428 109.977L172.476 109.989"
      stroke="#52525B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      opacity="0.25"
      d="M172.181 107.594L173.624 108.428"
      stroke="#52525B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      opacity="0.13"
      d="M176.386 106.626L176.379 107.793"
      stroke="#52525B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      width="12"
      height="3"
      rx="1.5"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 196.447 92.7925)"
      fill="#D4D4D8"
    />
    <rect
      x="0.00428286"
      y="-0.742904"
      width="33.5"
      height="65.5"
      rx="6.75"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 117.023 46.9146)"
      fill="#D4D4D8"
      stroke="#52525B"
      strokeWidth="1.5"
    />
    <rect
      x="0.00428286"
      y="-0.742904"
      width="33.5"
      height="65.5"
      rx="6.75"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 117.023 43.9146)"
      fill="white"
      stroke="#52525B"
      strokeWidth="1.5"
    />
    <rect
      width="12"
      height="3"
      rx="1.5"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 123.714 50.769)"
      fill="#D4D4D8"
    />
    <rect
      width="17"
      height="3"
      rx="1.5"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 97.5555 67.458)"
      fill="#D4D4D8"
    />
    <rect
      width="12"
      height="3"
      rx="1.5"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 93.1976 69.9092)"
      fill="#D4D4D8"
    />
    <path
      d="M92.3603 64.4564C90.9277 63.6287 88.59 63.6152 87.148 64.4264C85.7059 65.2375 85.6983 66.5703 87.1308 67.398C88.5634 68.2257 90.9011 68.2392 92.3432 67.428C93.7852 66.6169 93.7929 65.2841 92.3603 64.4564ZM88.4382 66.6626C87.7221 66.2488 87.726 65.5822 88.4468 65.1768C89.1676 64.7713 90.3369 64.7781 91.0529 65.1918C91.769 65.6055 91.7652 66.2722 91.0444 66.6776C90.3236 67.083 89.1543 67.0763 88.4382 66.6626Z"
      fill="#A1A1AA"
    />
    <rect
      width="17"
      height="3"
      rx="1.5"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 109.758 60.5942)"
      fill="#D4D4D8"
    />
    <rect
      width="12"
      height="3"
      rx="1.5"
      transform="matrix(0.865865 0.500278 -0.871576 0.490261 105.4 63.0454)"
      fill="#D4D4D8"
    />
    <path
      d="M104.562 57.5926C103.13 56.7649 100.792 56.7514 99.35 57.5626C97.908 58.3737 97.9003 59.7065 99.3329 60.5342C100.765 61.3619 103.103 61.3754 104.545 60.5642C105.987 59.7531 105.995 58.4203 104.562 57.5926ZM103.858 59.3971L100.815 59.6265C100.683 59.6366 100.55 59.6133 100.449 59.5629C100.44 59.5584 100.432 59.5544 100.424 59.5499C100.339 59.5004 100.29 59.4335 100.29 59.3637L100.293 58.62C100.294 58.4751 100.501 58.3585 100.756 58.3599C101.01 58.3614 101.216 58.48 101.216 58.6256L101.214 59.0669L103.732 58.8768C103.983 58.8577 104.217 58.9584 104.251 59.1029C104.286 59.2468 104.11 59.3787 103.858 59.3976L103.858 59.3971Z"
      fill="#A1A1AA"
    />
    <g clip-path="url(#clip0_20786_38285)">
      <path
        d="M133.106 82.3025L140.49 82.345L140.514 78.1353"
        stroke="#A1A1AA"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <g clip-path="url(#clip1_20786_38285)">
      <path
        d="M143.496 88.3059L150.88 88.3485L150.905 84.1387"
        stroke="#A1A1AA"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <g clip-path="url(#clip2_20786_38285)">
      <path
        d="M153.887 94.3093L161.271 94.3519L161.295 90.1421"
        stroke="#A1A1AA"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <g clip-path="url(#clip3_20786_38285)">
      <path
        d="M126.113 89.6911L118.729 89.6486L118.705 93.8583"
        stroke="#A1A1AA"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <g clip-path="url(#clip4_20786_38285)">
      <path
        d="M136.504 95.6945L129.12 95.652L129.095 99.8618"
        stroke="#A1A1AA"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <g clip-path="url(#clip5_20786_38285)">
      <path
        d="M146.894 101.698L139.51 101.655L139.486 105.865"
        stroke="#A1A1AA"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_20786_38285">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="matrix(0.865865 0.500278 -0.871576 0.490261 138.36 75.1509)"
        />
      </clipPath>
      <clipPath id="clip1_20786_38285">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="matrix(0.865865 0.500278 -0.871576 0.490261 148.75 81.1543)"
        />
      </clipPath>
      <clipPath id="clip2_20786_38285">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="matrix(0.865865 0.500278 -0.871576 0.490261 159.14 87.1577)"
        />
      </clipPath>
      <clipPath id="clip3_20786_38285">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="matrix(0.865865 0.500278 -0.871576 0.490261 120.928 84.9561)"
        />
      </clipPath>
      <clipPath id="clip4_20786_38285">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="matrix(0.865865 0.500278 -0.871576 0.490261 131.318 90.9595)"
        />
      </clipPath>
      <clipPath id="clip5_20786_38285">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="matrix(0.865865 0.500278 -0.871576 0.490261 141.709 96.9629)"
        />
      </clipPath>
    </defs>
  </svg>
)

export default TransferImage
