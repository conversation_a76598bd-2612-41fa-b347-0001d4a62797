'use client'

import { useState } from "react"
import { Heart, Search, User, ShoppingBag } from "lucide-react"
import { useParams } from "next/navigation"
import { StoreRegion } from "@medusajs/types"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import SearchModal from "@modules/common/components/search"
import { useWishlist } from "@lib/context/wishlist-context"

interface NavClientProps {
  // No props needed since we get countryCode from useParams
}

const NavClient = () => {
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const { countryCode } = useParams()
  const { getWishlistCount } = useWishlist()
  const wishlistCount = getWishlistCount()

  return (
    <>
      {/* Search Button */}
      <button
        onClick={() => setIsSearchOpen(true)}
        className="p-2 text-text-secondary hover:text-primary hover:bg-primary-50 rounded-lg transition-all duration-300 ease-brand hover:scale-110 active:scale-95"
      >
        <Search className="w-5 h-5" />
      </button>

      {/* Account */}
      <LocalizedClientLink
        href="/account"
        className="hidden sm:flex items-center gap-2 p-2 text-text-secondary hover:text-primary hover:bg-primary-50 rounded-lg transition-all duration-300 ease-brand hover:scale-110 active:scale-95"
        data-testid="nav-account-link"
      >
        <User className="w-5 h-5" />
        <span className="hidden md:block text-sm font-medium">Account</span>
      </LocalizedClientLink>

      {/* Wishlist */}
      <LocalizedClientLink
        href="/wishlist"
        className="p-2 text-text-secondary hover:text-primary hover:bg-primary-50 rounded-lg transition-all duration-300 ease-brand hover:scale-110 active:scale-95 relative"
      >
        <Heart className="w-5 h-5" />
        {wishlistCount > 0 && (
          <span className="absolute -top-1 -right-1 w-4 h-4 bg-accent text-white text-xs rounded-full flex items-center justify-center font-bold">
            {wishlistCount}
          </span>
        )}
      </LocalizedClientLink>

      {/* Search Modal */}
      <SearchModal
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
        countryCode={countryCode as string}
      />
    </>
  )
}

export default NavClient
