'use client'

import { HttpTypes } from "@medusajs/types"
import { getProductPrice } from "@lib/util/get-product-price"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import WishlistButton from "@modules/common/components/wishlist-button"
import CompareButton from "@modules/common/components/compare-button"
import Thumbnail from "../thumbnail"
import PreviewPrice from "../product-preview/price"

interface ProductCardProps {
  product: HttpTypes.StoreProduct
  region: HttpTypes.StoreRegion
  isFeatured?: boolean
  showActions?: boolean
}

const ProductCard = ({ 
  product, 
  region, 
  isFeatured = false, 
  showActions = true 
}: ProductCardProps) => {
  const { cheapestPrice } = getProductPrice({ product })

  return (
    <div className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 card-hover">
      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden">
        <LocalizedClientLink href={`/products/${product.handle}`}>
          <Thumbnail
            thumbnail={product.thumbnail}
            images={product.images}
            size="full"
            isFeatured={isFeatured}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </LocalizedClientLink>
        
        {/* Action Buttons */}
        {showActions && (
          <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <WishlistButton product={product} variant="icon-only" />
            <CompareButton product={product} variant="icon-only" />
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-6">
        <LocalizedClientLink href={`/products/${product.handle}`}>
          <h3 className="font-semibold text-lg text-text-primary mb-2 line-clamp-2 group-hover:text-primary transition-colors duration-300">
            {product.title}
          </h3>
        </LocalizedClientLink>
        
        {/* Category */}
        {product.categories && product.categories.length > 0 && (
          <p className="text-sm text-text-secondary mb-3">
            {product.categories[0].name}
          </p>
        )}

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          {cheapestPrice && (
            <PreviewPrice price={cheapestPrice} className="text-xl font-bold text-primary" />
          )}
        </div>

        {/* Action Buttons (Mobile) */}
        {showActions && (
          <div className="flex gap-2 sm:hidden">
            <WishlistButton product={product} variant="default" className="flex-1" />
            <CompareButton product={product} variant="icon-only" />
          </div>
        )}
      </div>
    </div>
  )
}

export default ProductCard
