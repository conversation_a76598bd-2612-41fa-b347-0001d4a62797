'use client'

import { HttpTypes } from "@medusajs/types"
import { getProductPrice } from "@lib/util/get-product-price"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import WishlistButton from "@modules/common/components/wishlist-button"
import CompareButton from "@modules/common/components/compare-button"
import Thumbnail from "../thumbnail"
import { ShoppingCart } from "lucide-react"
import { addToCart } from "@lib/data/cart"
import { useParams } from "next/navigation"
import { useState } from "react"
import { useToast } from "@lib/context/toast-context"

interface PetShopProductCardProps {
  product: HttpTypes.StoreProduct
  region: HttpTypes.StoreRegion
  isFeatured?: boolean
}

const PetShopProductCard = ({
  product,
  region,
  isFeatured = false
}: PetShopProductCardProps) => {
  const { cheapestPrice, variantPrice } = getProductPrice({ product })
  const countryCode = useParams().countryCode as string
  const [isAdding, setIsAdding] = useState(false)
  const { addToast } = useToast()

  // Calculate discount percentage
  const originalPrice = variantPrice?.original_amount
  const currentPrice = variantPrice?.calculated_amount
  const discountPercentage = originalPrice && currentPrice && originalPrice > currentPrice
    ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
    : 0

  // Check if product is new (created within last 30 days)
  // Use a stable date calculation to avoid hydration mismatch
  const isNew = product.created_at &&
    product.metadata?.is_new === 'true'

  // Check if product is hot (you can implement your own logic)
  const isHot = product.metadata?.is_hot === 'true'

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: region.currency_code || 'USD'
    }).format(amount / 100)
  }

  // Get the first available variant for quick add to cart
  const firstVariant = product.variants?.[0]

  // Handle add to cart
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!firstVariant?.id) {
      addToast({ type: "error", title: "Product variant not available" })
      return
    }

    setIsAdding(true)

    try {
      await addToCart({
        variantId: firstVariant.id,
        quantity: 1,
        countryCode,
      })
      addToast({ type: "success", title: "Product added to cart!" })
    } catch (error) {
      console.error("Error adding to cart:", error)
      addToast({ type: "error", title: "Failed to add product to cart" })
    } finally {
      setIsAdding(false)
    }
  }

  return (
    <div className="group bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300 relative animate-fade-in-up">
      {/* Badges */}
      <div className="absolute top-3 left-3 z-10 flex flex-col gap-1">
        {discountPercentage > 0 && (
          <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded animate-scale-in">
            -{discountPercentage}%
          </span>
        )}
        {isNew && (
          <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded animate-scale-in">
            NEW
          </span>
        )}
        {isHot && (
          <span className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded animate-scale-in">
            HOT
          </span>
        )}
      </div>

      {/* Action Buttons */}
      <div className="absolute top-3 right-3 z-10 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <WishlistButton product={product} variant="icon-only" className="bg-white shadow-md" />
        <CompareButton product={product} variant="icon-only" className="bg-white shadow-md" />
      </div>

      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden bg-gray-50">
        <LocalizedClientLink href={`/products/${product.handle}`}>
          <Thumbnail
            thumbnail={product.thumbnail}
            images={product.images}
            size="full"
            isFeatured={isFeatured}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            alt={`${product.title} - Pet product image`}
          />
        </LocalizedClientLink>
      </div>

      {/* Product Info */}
      <div className="p-4">
        <LocalizedClientLink href={`/products/${product.handle}`}>
          <h3 className="font-medium text-gray-900 mb-2 line-clamp-2 group-hover:text-orange-600 transition-colors duration-300">
            {product.title}
          </h3>
        </LocalizedClientLink>
        
        {/* Model Number */}
        {product.metadata?.model && (
          <p className="text-sm text-gray-500 mb-3">
            Model: {product.metadata.model}
          </p>
        )}

        {/* Price */}
        <div className="flex items-center gap-2 mb-4">
          {cheapestPrice && (
            <>
              <span className="text-lg font-bold text-gray-900">
                {formatPrice(cheapestPrice.calculated_amount)}
              </span>
              {originalPrice && originalPrice > cheapestPrice.calculated_amount && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(originalPrice)}
                </span>
              )}
            </>
          )}
        </div>

        {/* Add to Cart Button */}
        <button
          onClick={handleAddToCart}
          disabled={isAdding || !firstVariant}
          className="w-full bg-orange-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-orange-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center gap-2"
        >
          <ShoppingCart className="w-4 h-4" />
          {isAdding ? "Adding..." : "Add To Cart"}
        </button>
      </div>
    </div>
  )
}

export default PetShopProductCard
