/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Serif+Pro:wght@400;600;700&display=swap');

@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  input:focus ~ label,
  input:not(:placeholder-shown) ~ label {
    @apply -translate-y-2 text-xsmall-regular;
  }

  input:focus ~ label {
    @apply left-0;
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    border: 1px solid #212121;
    -webkit-text-fill-color: #212121;
    -webkit-box-shadow: 0 0 0px 1000px #fff inset;
    transition: background-color 5000s ease-in-out 0s;
  }

  input[type="search"]::-webkit-search-decoration,
  input[type="search"]::-webkit-search-cancel-button,
  input[type="search"]::-webkit-search-results-button,
  input[type="search"]::-webkit-search-results-decoration {
    -webkit-appearance: none;
  }
}

@layer components {
  .content-container {
    @apply max-w-[1440px] w-full mx-auto px-6;
  }

  .contrast-btn {
    @apply px-4 py-2 border border-black rounded-full hover:bg-black hover:text-white transition-colors duration-200 ease-in;
  }

  .text-xsmall-regular {
    @apply text-[10px] leading-4 font-normal;
  }

  .text-small-regular {
    @apply text-xs leading-5 font-normal;
  }

  .text-small-semi {
    @apply text-xs leading-5 font-semibold;
  }

  .text-base-regular {
    @apply text-sm leading-6 font-normal;
  }

  .text-base-semi {
    @apply text-sm leading-6 font-semibold;
  }

  .text-large-regular {
    @apply text-base leading-6 font-normal;
  }

  .text-large-semi {
    @apply text-base leading-6 font-semibold;
  }

  .text-xl-regular {
    @apply text-2xl leading-[36px] font-normal;
  }

  .text-xl-semi {
    @apply text-2xl leading-[36px] font-semibold;
  }

  .text-2xl-regular {
    @apply text-[30px] leading-[48px] font-normal;
  }

  .text-2xl-semi {
    @apply text-[30px] leading-[48px] font-semibold;
  }

  .text-3xl-regular {
    @apply text-[32px] leading-[44px] font-normal;
  }

  .text-3xl-semi {
    @apply text-[32px] leading-[44px] font-semibold;
  }

  /* Brand Components */
  .btn-primary {
    @apply bg-primary text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 ease-brand hover:bg-primary-600 hover:shadow-lg hover:shadow-primary/25 active:scale-95;
  }

  .btn-secondary {
    @apply bg-secondary text-text-primary px-6 py-3 rounded-lg font-medium transition-all duration-300 ease-brand hover:bg-secondary-300 active:scale-95;
  }

  .btn-accent {
    @apply bg-accent text-text-primary px-6 py-3 rounded-lg font-medium transition-all duration-300 ease-brand hover:bg-accent-600 hover:shadow-lg hover:shadow-accent/25 active:scale-95;
  }

  .card-hover {
    @apply transition-all duration-300 ease-brand hover:shadow-xl hover:-translate-y-2 hover:shadow-primary/10;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }

  .hero-title {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight;
  }

  .hero-subtitle {
    @apply text-lg md:text-xl text-text-secondary leading-relaxed;
  }

  .section-title {
    @apply text-3xl md:text-4xl font-bold text-text-primary mb-4;
  }

  .section-subtitle {
    @apply text-lg text-text-secondary mb-8;
  }

  /* Animation utilities */
  .stagger-children > * {
    animation-delay: calc(var(--stagger-delay, 100ms) * var(--index, 0));
  }

  /* Parallax container */
  .parallax-container {
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  .parallax-element {
    transform: translateZ(0);
    will-change: transform;
  }
}
