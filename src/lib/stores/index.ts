// Export all stores
export { useCartStore } from './cart-store'
export { usePreferencesStore } from './preferences-store'
export { useProductFilterStore } from './product-filter-store'

// Export types
export type { CartItem } from './cart-store'

// Store utilities
export const resetAllStores = () => {
  // Reset all stores to initial state
  useCartStore.getState().clearCart()
  useProductFilterStore.getState().clearAllFilters()
  // Note: Preferences store is typically not reset as it contains user settings
}

// Development helpers
export const getStoreStates = () => {
  if (process.env.NODE_ENV === 'development') {
    return {
      cart: useCartStore.getState(),
      preferences: usePreferencesStore.getState(),
      productFilter: useProductFilterStore.getState(),
    }
  }
  return null
}
