'use client'

import { useCartStore } from '../../lib/stores'
import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon, ShoppingBagIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/outline'

export default function CartDrawer() {
  const {
    isOpen,
    items,
    itemCount,
    subtotal,
    closeCart,
    updateQuantity,
    removeItem
  } = useCartStore()

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price / 100)
  }

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeCart}>
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                  <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                    {/* Header */}
                    <div className="flex items-start justify-between p-4 border-b">
                      <Dialog.Title className="text-lg font-medium text-gray-900">
                        Shopping Cart ({itemCount})
                      </Dialog.Title>
                      <div className="ml-3 flex h-7 items-center">
                        <button
                          type="button"
                          className="relative -m-2 p-2 text-gray-400 hover:text-gray-500"
                          onClick={closeCart}
                        >
                          <span className="absolute -inset-0.5" />
                          <span className="sr-only">Close panel</span>
                          <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                        </button>
                      </div>
                    </div>

                    {/* Cart Items */}
                    <div className="flex-1 overflow-y-auto p-4">
                      {items.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                          <ShoppingBagIcon className="h-12 w-12 mb-4" />
                          <p className="text-lg font-medium">Your cart is empty</p>
                          <p className="text-sm">Add some products to get started!</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {items.map((item) => (
                            <div key={item.variant_id} className="flex items-center space-x-4 bg-gray-50 p-4 rounded-lg">
                              {/* Product Image */}
                              <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                                {item.thumbnail ? (
                                  <img
                                    src={item.thumbnail}
                                    alt={item.title}
                                    className="h-full w-full object-cover object-center"
                                  />
                                ) : (
                                  <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                                    <ShoppingBagIcon className="h-8 w-8 text-gray-400" />
                                  </div>
                                )}
                              </div>

                              {/* Product Details */}
                              <div className="flex-1 min-w-0">
                                <h4 className="text-sm font-medium text-gray-900 truncate">
                                  {item.title}
                                </h4>
                                <p className="text-sm text-gray-500">
                                  {formatPrice(item.unit_price)}
                                </p>
                                
                                {/* Quantity Controls */}
                                <div className="flex items-center mt-2 space-x-2">
                                  <button
                                    onClick={() => updateQuantity(item.variant_id, item.quantity - 1)}
                                    className="p-1 rounded-md border border-gray-300 hover:bg-gray-100"
                                    disabled={item.quantity <= 1}
                                  >
                                    <MinusIcon className="h-4 w-4" />
                                  </button>
                                  <span className="px-2 py-1 text-sm font-medium min-w-[2rem] text-center">
                                    {item.quantity}
                                  </span>
                                  <button
                                    onClick={() => updateQuantity(item.variant_id, item.quantity + 1)}
                                    className="p-1 rounded-md border border-gray-300 hover:bg-gray-100"
                                  >
                                    <PlusIcon className="h-4 w-4" />
                                  </button>
                                </div>
                              </div>

                              {/* Item Total & Remove */}
                              <div className="flex flex-col items-end space-y-2">
                                <p className="text-sm font-medium text-gray-900">
                                  {formatPrice(item.total)}
                                </p>
                                <button
                                  onClick={() => removeItem(item.variant_id)}
                                  className="text-xs text-red-600 hover:text-red-500"
                                >
                                  Remove
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Footer */}
                    {items.length > 0 && (
                      <div className="border-t border-gray-200 p-4 space-y-4">
                        {/* Subtotal */}
                        <div className="flex justify-between text-base font-medium text-gray-900">
                          <p>Subtotal</p>
                          <p>{formatPrice(subtotal)}</p>
                        </div>
                        <p className="text-sm text-gray-500">
                          Shipping and taxes calculated at checkout.
                        </p>
                        
                        {/* Checkout Button */}
                        <button
                          type="button"
                          className="w-full bg-indigo-600 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          Checkout
                        </button>
                        
                        {/* Continue Shopping */}
                        <button
                          type="button"
                          onClick={closeCart}
                          className="w-full bg-white border border-gray-300 rounded-md py-2 px-8 flex items-center justify-center text-sm font-medium text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          Continue Shopping
                        </button>
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
