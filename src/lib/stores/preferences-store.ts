import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

type Theme = 'light' | 'dark' | 'system'
type Currency = 'usd' | 'eur' | 'gbp' | 'cad'
type Language = 'en' | 'es' | 'fr' | 'de'

interface PreferencesState {
  // State
  theme: Theme
  currency: Currency
  language: Language
  country: string
  region: string
  showPricesWithTax: boolean
  emailNotifications: boolean
  smsNotifications: boolean
  marketingEmails: boolean
  
  // Recently viewed products
  recentlyViewed: string[]
  
  // Wishlist
  wishlist: string[]
  
  // Actions
  setTheme: (theme: Theme) => void
  setCurrency: (currency: Currency) => void
  setLanguage: (language: Language) => void
  setCountry: (country: string) => void
  setRegion: (region: string) => void
  togglePricesWithTax: () => void
  toggleEmailNotifications: () => void
  toggleSmsNotifications: () => void
  toggleMarketingEmails: () => void
  
  // Recently viewed actions
  addToRecentlyViewed: (productId: string) => void
  clearRecentlyViewed: () => void
  
  // Wishlist actions
  addToWishlist: (productId: string) => void
  removeFromWishlist: (productId: string) => void
  toggleWishlist: (productId: string) => void
  clearWishlist: () => void
  isInWishlist: (productId: string) => boolean
}

export const usePreferencesStore = create<PreferencesState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        theme: 'system',
        currency: 'usd',
        language: 'en',
        country: 'us',
        region: 'us',
        showPricesWithTax: false,
        emailNotifications: true,
        smsNotifications: false,
        marketingEmails: true,
        recentlyViewed: [],
        wishlist: [],

        // Theme and display actions
        setTheme: (theme) => {
          set({ theme })
          
          // Apply theme to document
          if (typeof window !== 'undefined') {
            const root = window.document.documentElement
            root.classList.remove('light', 'dark')
            
            if (theme === 'system') {
              const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
              root.classList.add(systemTheme)
            } else {
              root.classList.add(theme)
            }
          }
        },

        setCurrency: (currency) => set({ currency }),
        setLanguage: (language) => set({ language }),
        setCountry: (country) => set({ country }),
        setRegion: (region) => set({ region }),

        // Notification preferences
        togglePricesWithTax: () => {
          set((state) => ({ showPricesWithTax: !state.showPricesWithTax }))
        },

        toggleEmailNotifications: () => {
          set((state) => ({ emailNotifications: !state.emailNotifications }))
        },

        toggleSmsNotifications: () => {
          set((state) => ({ smsNotifications: !state.smsNotifications }))
        },

        toggleMarketingEmails: () => {
          set((state) => ({ marketingEmails: !state.marketingEmails }))
        },

        // Recently viewed actions
        addToRecentlyViewed: (productId) => {
          set((state) => {
            const filtered = state.recentlyViewed.filter(id => id !== productId)
            const updated = [productId, ...filtered].slice(0, 10) // Keep only last 10
            return { recentlyViewed: updated }
          })
        },

        clearRecentlyViewed: () => {
          set({ recentlyViewed: [] })
        },

        // Wishlist actions
        addToWishlist: (productId) => {
          set((state) => {
            if (!state.wishlist.includes(productId)) {
              return { wishlist: [...state.wishlist, productId] }
            }
            return state
          })
        },

        removeFromWishlist: (productId) => {
          set((state) => ({
            wishlist: state.wishlist.filter(id => id !== productId)
          }))
        },

        toggleWishlist: (productId) => {
          const state = get()
          if (state.wishlist.includes(productId)) {
            state.removeFromWishlist(productId)
          } else {
            state.addToWishlist(productId)
          }
        },

        clearWishlist: () => {
          set({ wishlist: [] })
        },

        isInWishlist: (productId) => {
          const state = get()
          return state.wishlist.includes(productId)
        }
      }),
      {
        name: 'pet-store-preferences', // localStorage key
      }
    ),
    {
      name: 'preferences-store', // DevTools name
    }
  )
)

// Initialize theme on app start
if (typeof window !== 'undefined') {
  const store = usePreferencesStore.getState()
  store.setTheme(store.theme)
}
