import { MigrationInterface, QueryRunner, Table, Column } from "typeorm"

export class AddProductEnhancements1704067200000 implements MigrationInterface {
  name = 'AddProductEnhancements1704067200000'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create product_features table
    await queryRunner.createTable(
      new Table({
        name: "product_features",
        columns: [
          {
            name: "id",
            type: "varchar",
            isPrimary: true,
          },
          {
            name: "product_id",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "title",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "description",
            type: "text",
            isNullable: true,
          },
          {
            name: "icon",
            type: "varchar",
            isNullable: true,
          },
          {
            name: "order",
            type: "int",
            default: 0,
          },
          {
            name: "created_at",
            type: "timestamptz",
            default: "now()",
          },
          {
            name: "updated_at",
            type: "timestamptz",
            default: "now()",
          },
        ],
        foreignKeys: [
          {
            columnNames: ["product_id"],
            referencedTableName: "product",
            referencedColumnNames: ["id"],
            onDelete: "CASCADE",
          },
        ],
        indices: [
          {
            name: "IDX_product_features_product_id",
            columnNames: ["product_id"],
          },
        ],
      }),
      true
    )

    // Create product_specifications table
    await queryRunner.createTable(
      new Table({
        name: "product_specifications",
        columns: [
          {
            name: "id",
            type: "varchar",
            isPrimary: true,
          },
          {
            name: "product_id",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "key",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "value",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "order",
            type: "int",
            default: 0,
          },
          {
            name: "created_at",
            type: "timestamptz",
            default: "now()",
          },
          {
            name: "updated_at",
            type: "timestamptz",
            default: "now()",
          },
        ],
        foreignKeys: [
          {
            columnNames: ["product_id"],
            referencedTableName: "product",
            referencedColumnNames: ["id"],
            onDelete: "CASCADE",
          },
        ],
        indices: [
          {
            name: "IDX_product_specifications_product_id",
            columnNames: ["product_id"],
          },
        ],
      }),
      true
    )

    // Create product_faq table
    await queryRunner.createTable(
      new Table({
        name: "product_faq",
        columns: [
          {
            name: "id",
            type: "varchar",
            isPrimary: true,
          },
          {
            name: "product_id",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "question",
            type: "text",
            isNullable: false,
          },
          {
            name: "answer",
            type: "text",
            isNullable: false,
          },
          {
            name: "order",
            type: "int",
            default: 0,
          },
          {
            name: "created_at",
            type: "timestamptz",
            default: "now()",
          },
          {
            name: "updated_at",
            type: "timestamptz",
            default: "now()",
          },
        ],
        foreignKeys: [
          {
            columnNames: ["product_id"],
            referencedTableName: "product",
            referencedColumnNames: ["id"],
            onDelete: "CASCADE",
          },
        ],
        indices: [
          {
            name: "IDX_product_faq_product_id",
            columnNames: ["product_id"],
          },
        ],
      }),
      true
    )

    // Add marketing fields to product table
    await queryRunner.addColumns("product", [
      new Column({
        name: "discount_percentage",
        type: "int",
        isNullable: true,
      }),
      new Column({
        name: "warranty_info",
        type: "varchar",
        isNullable: true,
      }),
      new Column({
        name: "shipping_info",
        type: "varchar",
        isNullable: true,
      }),
      new Column({
        name: "trust_badges",
        type: "text",
        isNullable: true,
      }),
      new Column({
        name: "payment_methods",
        type: "text",
        isNullable: true,
      }),
      new Column({
        name: "highlights",
        type: "text",
        isNullable: true,
      }),
    ])
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove added columns from product table
    await queryRunner.dropColumns("product", [
      "discount_percentage",
      "warranty_info", 
      "shipping_info",
      "trust_badges",
      "payment_methods",
      "highlights",
    ])

    // Drop tables
    await queryRunner.dropTable("product_faq")
    await queryRunner.dropTable("product_specifications")
    await queryRunner.dropTable("product_features")
  }
}
