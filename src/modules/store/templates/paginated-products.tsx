"use client"

import { useEffect, useState } from "react"
import PetShopProductCard from "@modules/products/components/petshop-product-card"
import { Pagination } from "@modules/store/components/pagination"
import { SortOptions } from "@modules/store/components/refinement-list/sort-products"
import { HttpTypes } from "@medusajs/types"

const PRODUCT_LIMIT = 12

type PaginatedProductsParams = {
  limit: number
  collection_id?: string[]
  category_id?: string[]
  id?: string[]
  order?: string
}

export default function PaginatedProducts({
  sortBy,
  page,
  collectionId,
  categoryId,
  productsIds,
  countryCode,
}: {
  sortBy?: SortOptions
  page: number
  collectionId?: string
  categoryId?: string
  productsIds?: string[]
  countryCode: string
}) {
  const [products, setProducts] = useState<HttpTypes.StoreProduct[]>([])
  const [count, setCount] = useState(0)
  const [region, setRegion] = useState<HttpTypes.StoreRegion | null>(null)
  const [loading, setLoading] = useState(true)
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        // Build query parameters
        const queryParams: PaginatedProductsParams = {
          limit: 12,
        }

        if (collectionId) {
          queryParams["collection_id"] = [collectionId]
        }

        if (categoryId) {
          queryParams["category_id"] = [categoryId]
        }

        if (productsIds) {
          queryParams["id"] = productsIds
        }

        if (sortBy === "created_at") {
          queryParams["order"] = "created_at"
        }

        // Fetch region
        const regionResponse = await fetch(`/api/regions/${countryCode}`)
        if (!regionResponse.ok) {
          throw new Error('Failed to fetch region')
        }
        const regionData = await regionResponse.json()
        setRegion(regionData.region)

        // Fetch products
        const productsResponse = await fetch(`/api/products?${new URLSearchParams({
          page: page.toString(),
          countryCode,
          sortBy: sortBy || 'created_at',
          ...(collectionId && { collectionId }),
          ...(categoryId && { categoryId }),
          ...(productsIds && { productsIds: productsIds.join(',') }),
        })}`)

        if (!productsResponse.ok) {
          throw new Error('Failed to fetch products')
        }

        const productsData = await productsResponse.json()
        setProducts(productsData.products || [])
        setCount(productsData.count || 0)
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [sortBy, page, collectionId, categoryId, productsIds, countryCode])

  const totalPages = Math.ceil(count / PRODUCT_LIMIT)

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(12)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
            <div className="bg-gray-200 h-4 rounded mb-2"></div>
            <div className="bg-gray-200 h-4 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    )
  }

  if (!region) {
    return null
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No products found</p>
      </div>
    )
  }

  return (
    <>
      {/* Products Grid - PetShop Style */}
      <div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
        data-testid="products-list"
      >
        {products.map((p) => {
          return (
            <PetShopProductCard
              key={p.id}
              product={p}
              region={region}
            />
          )
        })}
      </div>

      {/* Load More Button - PetShop Style */}
      {totalPages > 1 && (
        <div className="mt-12 text-center">
          <div className="flex items-center justify-center gap-2 mb-6">
            {/* Pagination Numbers */}
            <div className="flex items-center gap-1">
              {page > 1 && (
                <button className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-orange-500">
                  ←
                </button>
              )}

              {[...Array(Math.min(5, totalPages))].map((_, i) => {
                const pageNum = Math.max(1, page - 2) + i
                if (pageNum > totalPages) return null

                return (
                  <button
                    key={pageNum}
                    className={`w-8 h-8 flex items-center justify-center rounded ${
                      pageNum === page
                        ? 'bg-orange-500 text-white'
                        : 'text-gray-600 hover:text-orange-500'
                    }`}
                  >
                    {pageNum}
                  </button>
                )
              })}

              {page < totalPages && (
                <button className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-orange-500">
                  →
                </button>
              )}
            </div>
          </div>

          {/* Load More Button */}
          <button className="bg-orange-500 text-white px-8 py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors duration-200">
            Load more
          </button>
        </div>
      )}
    </>
  )
}
