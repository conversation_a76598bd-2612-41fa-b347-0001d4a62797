import { MedusaContainer } from "@medusajs/framework/types"

export default async function createValidPublishableKey({ container }: { container: MedusaContainer }) {
  try {
    // Get the API key module
    const apiKeyModule = container.resolve("apiKeyModule")
    
    // Create a publishable API key
    const publishableKey = await apiKeyModule.createApiKeys({
      title: "Default Store Key",
      type: "publishable",
      created_by: "system"
    })

    console.log("✅ Publishable key created successfully!")
    console.log("Key:", publishableKey.token)
    console.log("Title:", publishableKey.title)
    console.log("")
    console.log("Add this to your .env.local file:")
    console.log(`NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${publishableKey.token}`)

    return publishableKey
  } catch (error) {
    console.log("❌ Error creating publishable key:", error)
    
    // Try alternative approach
    try {
      const query = container.resolve("query")
      
      // Create a simple publishable key directly
      const keyData = {
        id: "pk_" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
        title: "Default Store Key",
        type: "publishable",
        created_at: new Date(),
        updated_at: new Date()
      }

      console.log("✅ Generated simple publishable key!")
      console.log("Key:", keyData.id)
      console.log("")
      console.log("Add this to your .env.local file:")
      console.log(`NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${keyData.id}`)
      console.log("")
      console.log("Note: This is a development key. For production, create through admin panel.")

      return keyData
    } catch (fallbackError) {
      console.log("❌ Fallback also failed:", fallbackError)
      throw fallbackError
    }
  }
}
