'use client'

import { Scale, X, Eye } from 'lucide-react'
import { useCompare } from '@lib/context/compare-context'
import LocalizedClientLink from '@modules/common/components/localized-client-link'

const CompareBar = () => {
  const { state, removeFromCompare, clearCompare } = useCompare()
  const { items } = state

  if (items.length === 0) return null

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40 animate-slide-up">
      <div className="bg-white rounded-2xl shadow-2xl border border-secondary-200 p-4 max-w-4xl">
        <div className="flex items-center gap-4">
          {/* Icon and Count */}
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
              <Scale className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="font-semibold text-text-primary">
                {items.length} Product{items.length !== 1 ? 's' : ''} to Compare
              </div>
              <div className="text-sm text-text-secondary">
                {4 - items.length} more can be added
              </div>
            </div>
          </div>

          {/* Product Thumbnails */}
          <div className="flex gap-2 flex-1 overflow-x-auto">
            {items.map((item) => (
              <div key={item.id} className="relative flex-shrink-0">
                <div className="w-12 h-12 rounded-lg overflow-hidden border-2 border-secondary-200">
                  <img
                    src={item.thumbnail || 'https://via.placeholder.com/48x48/F2F2F2/666666?text=?'}
                    alt={item.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <button
                  onClick={() => removeFromCompare(item.id)}
                  className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors duration-300"
                >
                  <X className="w-2.5 h-2.5" />
                </button>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <LocalizedClientLink
              href="/compare"
              className="flex items-center gap-2 bg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-600 transition-colors duration-300"
            >
              <Eye className="w-4 h-4" />
              Compare
            </LocalizedClientLink>
            
            <button
              onClick={clearCompare}
              className="p-2 text-text-secondary hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-300"
              title="Clear all"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CompareBar
