'use client'

import { HttpTypes } from "@medusajs/types"
import ProductCard from "@modules/products/components/product-card"
import ProductPreview from "@modules/products/components/product-preview"

interface ProductGridProps {
  products: HttpTypes.StoreProduct[]
  region: HttpTypes.StoreRegion
  viewMode?: 'grid' | 'list'
  showActions?: boolean
}

const ProductGrid = ({ 
  products, 
  region, 
  viewMode = 'grid', 
  showActions = true 
}: ProductGridProps) => {
  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📦</div>
        <h3 className="text-xl font-semibold text-text-primary mb-2">No products found</h3>
        <p className="text-text-secondary">Try adjusting your filters or search terms.</p>
      </div>
    )
  }

  return (
    <div className={`grid gap-6 ${
      viewMode === 'grid' 
        ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
        : 'grid-cols-1'
    }`}>
      {products.map((product) => (
        <div key={product.id}>
          {showActions ? (
            <ProductCard 
              product={product} 
              region={region} 
              showActions={showActions}
            />
          ) : (
            <ProductPreview 
              product={product} 
              region={region} 
            />
          )}
        </div>
      ))}
    </div>
  )
}

export default ProductGrid
