const axios = require('axios');

const MEDUSA_BACKEND_URL = 'http://localhost:9000';
let JWT_TOKEN = null;

// 登录获取JWT token
async function login() {
  try {
    const response = await axios.post(`${MEDUSA_BACKEND_URL}/auth/user/emailpass`, {
      email: '<EMAIL>',
      password: 'supersecret'
    });
    JWT_TOKEN = response.data.token;
    console.log('✅ Login successful');
    return JWT_TOKEN;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

// 获取所有产品
async function getAllProducts() {
  try {
    const response = await axios.get(`${MEDUSA_BACKEND_URL}/admin/products`, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`
      }
    });
    return response.data.products;
  } catch (error) {
    console.error('❌ Error fetching products:', error.response?.data || error.message);
    return [];
  }
}

// 更新产品的增强数据
async function updateProductWithEnhancedData(productId, enhancedData) {
  try {
    const response = await axios.post(`${MEDUSA_BACKEND_URL}/admin/products/${productId}`, {
      metadata: enhancedData
    }, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ Updated product ${productId} with enhanced data`);
    return response.data.product;
  } catch (error) {
    console.error(`❌ Error updating product ${productId}:`, error.response?.data || error.message);
    return null;
  }
}

// PetShop 风格的产品增强数据模板
const getEnhancedDataTemplate = (productTitle) => {
  const templates = {
    'feeder': {
      features: [
        {
          title: "Smart APP Control",
          description: "Remotely set times to feed your pet automatically with smartphone app control",
          icon: "📱"
        },
        {
          title: "HD Camera with Night Vision", 
          description: "1080p HD camera allows you to see and hear your pet during mealtime",
          icon: "📹"
        },
        {
          title: "WiFi Compatible",
          description: "Compatible with both 2.4G and 5G WiFi networks for reliable connection",
          icon: "📶"
        },
        {
          title: "Large Capacity",
          description: "7L tank capacity provides food for multiple days",
          icon: "🥣"
        },
        {
          title: "Dual Power Mode",
          description: "AC adapter with battery backup ensures feeding even during power outages",
          icon: "🔋"
        },
        {
          title: "Food Jam Detection",
          description: "Infrared sensor prevents food jamming and alerts you via app",
          icon: "⚠️"
        }
      ],
      specifications: {
        capacity: "7L / 29 cups",
        wifi: "2.4G & 5G Compatible",
        mealRange: "1-6 meals per day",
        portionRange: "1-20 portions per meal",
        portionSize: "10g per portion",
        kibbleSize: "2-15mm diameter",
        camera: "1080p HD with Night Vision",
        powerSupply: "AC Adapter + 3×D Battery Backup",
        dimensions: "9.8 × 13.7 × 15.3 inch"
      },
      faq: [
        {
          question: "How much food does the feeder dispense in a day at most?",
          answer: "You can program the feeder to dispense up to 6 meals a day with up to 20 portions per meal."
        },
        {
          question: "What happens if the power goes out?",
          answer: "The feeder has dual power mode with battery backup. It will continue feeding during power outages and resume normal schedule when power returns."
        },
        {
          question: "What is the maximum food size?",
          answer: "The feeder is suitable for kibble sizes ranging from 2-15mm in diameter."
        },
        {
          question: "How to set up the WiFi connection?",
          answer: "Download the PetShop app, register an account, add the device, enter your WiFi password, and use QR code to connect the camera."
        }
      ],
      marketing: {
        discount: "23",
        paymentMethods: "PayPal, Google Pay, Apple Pay, Visa, Mastercard, American Express",
        trustBadges: "Fast Shipping, 24-Month Extended Warranty, 30-day Money Back, 24/7 Customer Support",
        cta: "ADD TO CART",
        highlights: "Smart APP Control, HD Camera with Night Vision, WiFi Compatible (2.4G & 5G), Large 7L Capacity, Dual Power Mode, Food Jam Detection"
      },
      warranty: "24-Month Extended Warranty",
      shipping: "Fast Shipping - Get Your Package in 3-5 days"
    },
    'fountain': {
      features: [
        {
          title: "Stainless Steel Construction",
          description: "Premium 304 stainless steel for durability and easy cleaning",
          icon: "🔧"
        },
        {
          title: "Multi-Level Water Flow",
          description: "Encourages pets to drink more with flowing water design",
          icon: "💧"
        },
        {
          title: "Ultra-Quiet Operation",
          description: "Whisper-quiet pump operates below 30dB for peaceful environment",
          icon: "🔇"
        },
        {
          title: "Large Capacity",
          description: "3.2L capacity perfect for multiple pets or large breeds",
          icon: "🥤"
        },
        {
          title: "LED Water Level Indicator",
          description: "Visual indicator shows when it's time to refill",
          icon: "💡"
        },
        {
          title: "Replaceable Filter",
          description: "Carbon filter removes impurities and improves taste",
          icon: "🔄"
        }
      ],
      specifications: {
        capacity: "3.2L",
        material: "304 Stainless Steel",
        noiseLevel: "< 30dB",
        powerConsumption: "2W",
        filterType: "Replaceable Carbon Filter",
        dimensions: "10 × 10 × 8 inch",
        weight: "2.5 lbs",
        powerSupply: "AC Adapter"
      },
      faq: [
        {
          question: "How often should I clean the fountain?",
          answer: "We recommend cleaning the fountain weekly and replacing the filter every 2-4 weeks depending on usage."
        },
        {
          question: "Is it suitable for large dogs?",
          answer: "Yes, the 3.2L capacity and multi-level design make it perfect for large breeds and multiple pets."
        },
        {
          question: "How quiet is the pump?",
          answer: "The ultra-quiet pump operates below 30dB, which is quieter than a whisper."
        }
      ],
      marketing: {
        discount: "15",
        paymentMethods: "PayPal, Google Pay, Apple Pay, Visa, Mastercard",
        trustBadges: "Fast Shipping, 12-Month Warranty, 30-day Money Back, 24/7 Support",
        cta: "ADD TO CART",
        highlights: "Stainless Steel Construction, Multi-Level Flow, Ultra-Quiet Operation, Large 3.2L Capacity"
      },
      warranty: "12-Month Warranty",
      shipping: "Fast Shipping - 2-4 business days"
    },
    'toy': {
      features: [
        {
          title: "Interactive Motion Sensors",
          description: "Responds to your pet's movement for engaging play sessions",
          icon: "🎯"
        },
        {
          title: "Multiple Play Modes",
          description: "Various automatic play patterns to keep pets entertained",
          icon: "🎮"
        },
        {
          title: "Durable Construction",
          description: "Built to withstand active play and pet interaction",
          icon: "💪"
        },
        {
          title: "Battery Powered",
          description: "Cordless design for safe and convenient placement anywhere",
          icon: "🔋"
        }
      ],
      specifications: {
        playModes: "5 Different Modes",
        batteryLife: "Up to 30 days",
        material: "Pet-Safe ABS Plastic",
        dimensions: "6 × 6 × 4 inch",
        weight: "1.2 lbs",
        ageRange: "All Ages"
      },
      faq: [
        {
          question: "Is it safe for cats and dogs?",
          answer: "Yes, it's made from pet-safe materials and designed for both cats and dogs of all sizes."
        },
        {
          question: "How long does the battery last?",
          answer: "With normal use, the battery can last up to 30 days before needing replacement."
        }
      ],
      marketing: {
        discount: "20",
        paymentMethods: "PayPal, Google Pay, Visa, Mastercard",
        trustBadges: "Fast Shipping, 6-Month Warranty, 30-day Money Back",
        cta: "ADD TO CART",
        highlights: "Interactive Motion Sensors, Multiple Play Modes, Durable Construction"
      },
      warranty: "6-Month Warranty",
      shipping: "Standard Shipping - 3-7 days"
    }
  };

  // 根据产品标题判断类型
  const title = productTitle.toLowerCase();
  if (title.includes('feeder') || title.includes('feed')) {
    return templates.feeder;
  } else if (title.includes('fountain') || title.includes('water')) {
    return templates.fountain;
  } else if (title.includes('toy') || title.includes('play')) {
    return templates.toy;
  } else {
    return templates.toy; // 默认使用玩具模板
  }
};

async function main() {
  console.log('🚀 Starting product enhancement with PetShop-style data...');
  
  // 先登录
  await login();
  
  // 获取所有产品
  const products = await getAllProducts();
  console.log(`📦 Found ${products.length} products to enhance`);
  
  for (const product of products) {
    console.log(`\n🔄 Processing product: ${product.title}`);
    
    // 获取适合的增强数据模板
    const enhancedData = getEnhancedDataTemplate(product.title);
    
    // 更新产品
    await updateProductWithEnhancedData(product.id, enhancedData);
    
    // 等待1秒避免API限制
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n✅ Product enhancement completed!');
  console.log('\n🎯 You can now view enhanced products at:');
  console.log('   - Regular view: http://localhost:8000/us/products/[handle]');
  console.log('   - PetShop style: http://localhost:8000/us/products/[handle]/petshop-style');
}

// 运行脚本
main().catch(console.error);
