import { getBaseURL } from "@lib/util/env"
import { Metadata } from "next"
import "styles/globals.css"

export const metadata: Metadata = {
  metadataBase: new URL(getBaseURL()),
  title: {
    default: "PetStore - Premium Pet Care Products",
    template: "%s | PetStore"
  },
  description: "Discover premium pet care products including smart feeders, water fountains, toys, and accessories. Quality products for your beloved pets.",
  keywords: ["pet store", "pet products", "smart feeders", "pet toys", "pet accessories", "pet care"],
  authors: [{ name: "PetStore Team" }],
  creator: "PetStore",
  publisher: "PetStore",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: getBaseURL(),
    title: "PetStore - Premium Pet Care Products",
    description: "Discover premium pet care products including smart feeders, water fountains, toys, and accessories.",
    siteName: "PetStore",
  },
  twitter: {
    card: "summary_large_image",
    title: "PetStore - Premium Pet Care Products",
    description: "Discover premium pet care products including smart feeders, water fountains, toys, and accessories.",
    creator: "@petstore",
  },
  verification: {
    google: "google-site-verification-code",
  },
}

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" data-mode="light">
      <body suppressHydrationWarning={true}>
        <main className="relative">{props.children}</main>
      </body>
    </html>
  )
}
